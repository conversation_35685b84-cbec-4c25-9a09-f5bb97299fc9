{"appId": "com.meea.viofo", "productName": "MEEA-VIOFO", "directories": {"output": "dist"}, "files": ["dist/assets/**/*", "dist/index.html", "dist/*.svg", "dist/*.png", "electron/**/*", "keys/**/*", "package.json", "node_modules/fluent-ffmpeg/**/*", "node_modules/exiftool-vendored/**/*", "!node_modules/exiftool-vendored/bin/**/*", "!ffmpeg/**/*", "!exiftool/**/*", "!dist/mac/**/*", "!dist/mac-arm64/**/*", "!dist/linux/**/*", "!dist/win-*/**/*", "!dist/*.dmg", "!dist/*.zip", "!dist/*.exe", "!dist/*.AppImage", "!dist/*.tar.gz", "!dist/*.deb", "!dist/*.rpm", "!dist/builder-*.yml", "!dist/builder-*.yaml", "!node_modules/**/prebuilds/darwin-*/**/*", "!node_modules/**/prebuilds/linux-*/**/*", "!node_modules/**/prebuilds/win32-arm64/**/*", "!node_modules/**/bin/darwin-*/**/*", "!node_modules/**/bin/linux-*/**/*", "!node_modules/**/bin/win32-arm64/**/*"], "win": {"icon": "build/icons/icon.ico", "files": ["!ffmpeg/mac-*/**/*", "!ffmpeg/linux-*/**/*", "!ffmpeg/win-arm64/**/*", "!exiftool/mac-*/**/*", "!exiftool/linux-*/**/*", "!exiftool/win-arm64/**/*"], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "extraResources": [{"from": "ffmpeg/win-x64", "to": "ffmpeg/win-x64", "filter": ["**/*"]}, {"from": "exiftool/win-x64", "to": "exiftool/win-x64", "filter": ["**/*"]}], "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MEEA-VIOFO", "artifactName": "${productName}-Setup-${version}-windows-x64.${ext}", "installerIcon": "build/icons/icon.ico", "uninstallerIcon": "build/icons/icon.ico", "installerHeaderIcon": "build/icons/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Video", "include": "build/installer.nsh"}}