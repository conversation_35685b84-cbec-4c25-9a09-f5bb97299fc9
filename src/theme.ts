import { createSystem, defaultConfig } from '@chakra-ui/react';

// 使用正确的 Chakra UI v3 API
const theme = createSystem(defaultConfig, {
  tokens: {
    colors: {
      brand: {
        50: { value: '#e6f7f7' },
        100: { value: '#b3e8e8' },
        200: { value: '#80d9d9' },
        300: { value: '#4dcaca' },
        400: { value: '#1abbbb' },
        500: { value: '#43443F' }, // 主题色
        600: { value: '#369999' },
        700: { value: '#2a7373' },
        800: { value: '#1d4d4d' },
        900: { value: '#102626' },
      },
      teal: {
        50: { value: '#e6fffa' },
        100: { value: '#b2f5ea' },
        200: { value: '#81e6d9' },
        300: { value: '#4fd1c7' },
        400: { value: '#38b2ac' },
        500: { value: '#319795' },
        600: { value: '#2c7a7b' },
        700: { value: '#285e61' },
        800: { value: '#234e52' },
        900: { value: '#1d4044' },
      }
    },
    fonts: {
      heading: { value: `-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"` },
      body: { value: `-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"` },
    }
  }
});

export default theme;
