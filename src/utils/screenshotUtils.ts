/**
 * 截图工具函数
 */

/**
 * 格式化时间戳为文件名格式
 * @param seconds 时间戳（秒）
 * @returns 格式化的时间字符串，如 "01-23"
 */
export function formatTimeForFilename(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}-${secs.toString().padStart(2, '0')}`;
}

/**
 * 从视频文件名提取基础名称（去除扩展名）
 * @param videoPath 视频文件路径
 * @returns 基础文件名
 */
export function getVideoBaseName(videoPath: string): string {
  const fileName = videoPath.split('/').pop() || videoPath.split('\\').pop() || 'video';
  return fileName.replace(/\.[^/.]+$/, ''); // 去除扩展名
}

/**
 * 生成截图文件名
 * @param videoName 视频名称
 * @param timestamp 时间戳（秒）
 * @param isMultiVideo 是否为多视频截图
 * @returns 生成的文件名
 */
export function generateScreenshotFilename(
  videoName: string, 
  timestamp: number, 
  isMultiVideo: boolean = false
): string {
  const timeStr = formatTimeForFilename(timestamp);
  
  if (isMultiVideo) {
    return `截图_${timeStr}s.png`;
  } else {
    const baseName = getVideoBaseName(videoName);
    return `${baseName}_${timeStr}s.png`;
  }
}

/**
 * 将base64数据转换为Blob
 * @param base64Data base64数据字符串
 * @returns Blob对象
 */
export function base64ToBlob(base64Data: string): Blob {
  // 移除data:image/png;base64,前缀
  const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
  
  // 将base64转换为二进制数据
  const byteCharacters = atob(base64String);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: 'image/png' });
}

/**
 * 下载base64图片数据
 * @param base64Data base64数据字符串
 * @param filename 文件名
 */
export function downloadBase64Image(base64Data: string, filename: string): void {
  try {
    const blob = base64ToBlob(base64Data);
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载图片失败:', error);
    throw new Error('下载图片失败');
  }
}

/**
 * 使用系统保存对话框保存base64图片
 * @param base64Data base64数据字符串
 * @param defaultFilename 默认文件名
 * @returns Promise<boolean> 是否保存成功
 */
export async function saveBase64ImageWithDialog(
  base64Data: string, 
  defaultFilename: string
): Promise<boolean> {
  try {
    // 检查是否在Electron环境中
    if (!(window as any).electronAPI?.saveBase64Image) {
      // 如果不在Electron环境中，使用浏览器下载
      downloadBase64Image(base64Data, defaultFilename);
      return true;
    }
    
    // 使用Electron的保存对话框
    const result = await (window as any).electronAPI.saveBase64Image(base64Data, defaultFilename);
    return result.success && !result.cancelled;
  } catch (error) {
    console.error('保存图片失败:', error);
    throw error;
  }
}
