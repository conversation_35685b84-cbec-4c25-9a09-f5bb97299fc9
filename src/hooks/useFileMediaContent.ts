import { useState, useCallback } from 'react';

export interface MediaScreenshot {
  id: string;
  videoPath: string;
  timestamp: number;
  imagePath: string;
  createdAt: Date;
  thumbnailPath?: string;
  groupId?: string; // 用于多视频截图分组
}

export interface MediaClip {
  id: string;
  videoPath: string;
  startTime: number;
  endTime: number;
  outputPath: string;
  createdAt: Date;
  duration: number;
  groupId?: string; // 用于多视频剪辑分组
}

export interface MediaGroup {
  id: string;
  name: string;
  type: 'screenshot' | 'clip';
  createdAt: Date;
  videoPaths: string[]; // 包含的视频文件路径
  items: (MediaScreenshot | MediaClip)[];
}

export interface FileMediaContent {
  videoPath: string;
  screenshots: MediaScreenshot[];
  clips: MediaClip[];
  groups: MediaGroup[];
}

export interface UseFileMediaContentReturn {
  fileMediaContents: Map<string, FileMediaContent>;
  
  // 截图管理
  addScreenshot: (screenshot: Omit<MediaScreenshot, 'id' | 'createdAt'>) => void;
  removeScreenshot: (videoPath: string, screenshotId: string) => void;
  getScreenshotsForFile: (videoPath: string) => MediaScreenshot[];
  
  // 剪辑管理
  addClip: (clip: Omit<MediaClip, 'id' | 'createdAt'>) => void;
  removeClip: (videoPath: string, clipId: string) => void;
  getClipsForFile: (videoPath: string) => MediaClip[];
  
  // 分组管理
  createGroup: (group: Omit<MediaGroup, 'id' | 'createdAt'>) => string;
  removeGroup: (groupId: string) => void;
  getGroupsForFile: (videoPath: string) => MediaGroup[];
  getGroupById: (groupId: string) => MediaGroup | undefined;
  
  // 多视频内容管理
  addMultiVideoScreenshots: (screenshots: Omit<MediaScreenshot, 'id' | 'createdAt'>[], groupName?: string) => string;
  addMultiVideoClips: (clips: Omit<MediaClip, 'id' | 'createdAt'>[], groupName?: string) => string;
  
  // 清理
  clearContentForFile: (videoPath: string) => void;
  clearAllContent: () => void;
}

export const useFileMediaContent = (): UseFileMediaContentReturn => {
  const [fileMediaContents, setFileMediaContents] = useState<Map<string, FileMediaContent>>(new Map());

  // 获取或创建文件的媒体内容
  const getOrCreateFileContent = useCallback((videoPath: string): FileMediaContent => {
    const existing = fileMediaContents.get(videoPath);
    if (existing) return existing;
    
    return {
      videoPath,
      screenshots: [],
      clips: [],
      groups: []
    };
  }, [fileMediaContents]);

  // 添加截图
  const addScreenshot = useCallback((screenshot: Omit<MediaScreenshot, 'id' | 'createdAt'>) => {
    const newScreenshot: MediaScreenshot = {
      ...screenshot,
      id: `screenshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };
    
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      const content = getOrCreateFileContent(screenshot.videoPath);
      const updatedContent = {
        ...content,
        screenshots: [...content.screenshots, newScreenshot]
      };
      newMap.set(screenshot.videoPath, updatedContent);
      return newMap;
    });
  }, [getOrCreateFileContent]);

  // 删除截图
  const removeScreenshot = useCallback((videoPath: string, screenshotId: string) => {
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      const content = newMap.get(videoPath);
      if (content) {
        const updatedContent = {
          ...content,
          screenshots: content.screenshots.filter(s => s.id !== screenshotId)
        };
        newMap.set(videoPath, updatedContent);
      }
      return newMap;
    });
  }, []);

  // 获取文件的截图
  const getScreenshotsForFile = useCallback((videoPath: string) => {
    const content = fileMediaContents.get(videoPath);
    return content?.screenshots || [];
  }, [fileMediaContents]);

  // 添加剪辑
  const addClip = useCallback((clip: Omit<MediaClip, 'id' | 'createdAt'>) => {
    const newClip: MediaClip = {
      ...clip,
      id: `clip_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
    };
    
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      const content = getOrCreateFileContent(clip.videoPath);
      const updatedContent = {
        ...content,
        clips: [...content.clips, newClip]
      };
      newMap.set(clip.videoPath, updatedContent);
      return newMap;
    });
  }, [getOrCreateFileContent]);

  // 删除剪辑
  const removeClip = useCallback((videoPath: string, clipId: string) => {
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      const content = newMap.get(videoPath);
      if (content) {
        const updatedContent = {
          ...content,
          clips: content.clips.filter(c => c.id !== clipId)
        };
        newMap.set(videoPath, updatedContent);
      }
      return newMap;
    });
  }, []);

  // 获取文件的剪辑
  const getClipsForFile = useCallback((videoPath: string) => {
    const content = fileMediaContents.get(videoPath);
    return content?.clips || [];
  }, [fileMediaContents]);

  // 创建分组
  const createGroup = useCallback((group: Omit<MediaGroup, 'id' | 'createdAt'>) => {
    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newGroup: MediaGroup = {
      ...group,
      id: groupId,
      createdAt: new Date(),
    };
    
    // 将分组添加到所有相关文件中
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      
      group.videoPaths.forEach(videoPath => {
        const content = getOrCreateFileContent(videoPath);
        const updatedContent = {
          ...content,
          groups: [...content.groups, newGroup]
        };
        newMap.set(videoPath, updatedContent);
      });
      
      return newMap;
    });
    
    return groupId;
  }, [getOrCreateFileContent]);

  // 删除分组
  const removeGroup = useCallback((groupId: string) => {
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      
      // 从所有文件中删除该分组
      for (const [videoPath, content] of newMap.entries()) {
        const updatedContent = {
          ...content,
          groups: content.groups.filter(g => g.id !== groupId),
          screenshots: content.screenshots.map(s => 
            s.groupId === groupId ? { ...s, groupId: undefined } : s
          ),
          clips: content.clips.map(c => 
            c.groupId === groupId ? { ...c, groupId: undefined } : c
          )
        };
        newMap.set(videoPath, updatedContent);
      }
      
      return newMap;
    });
  }, []);

  // 获取文件的分组
  const getGroupsForFile = useCallback((videoPath: string) => {
    const content = fileMediaContents.get(videoPath);
    return content?.groups || [];
  }, [fileMediaContents]);

  // 根据ID获取分组
  const getGroupById = useCallback((groupId: string) => {
    for (const content of fileMediaContents.values()) {
      const group = content.groups.find(g => g.id === groupId);
      if (group) return group;
    }
    return undefined;
  }, [fileMediaContents]);

  // 添加多视频截图
  const addMultiVideoScreenshots = useCallback((
    screenshots: Omit<MediaScreenshot, 'id' | 'createdAt'>[],
    groupName?: string
  ) => {
    const videoPaths = [...new Set(screenshots.map(s => s.videoPath))];
    const groupId = createGroup({
      name: groupName || `截图 ${new Date().toLocaleString()}`,
      type: 'screenshot',
      videoPaths,
      items: []
    });
    
    // 添加截图并关联到分组
    screenshots.forEach(screenshot => {
      addScreenshot({ ...screenshot, groupId });
    });
    
    return groupId;
  }, [createGroup, addScreenshot]);

  // 添加多视频剪辑
  const addMultiVideoClips = useCallback((
    clips: Omit<MediaClip, 'id' | 'createdAt'>[],
    groupName?: string
  ) => {
    const videoPaths = [...new Set(clips.map(c => c.videoPath))];
    const groupId = createGroup({
      name: groupName || `剪辑 ${new Date().toLocaleString()}`,
      type: 'clip',
      videoPaths,
      items: []
    });
    
    // 添加剪辑并关联到分组
    clips.forEach(clip => {
      addClip({ ...clip, groupId });
    });
    
    return groupId;
  }, [createGroup, addClip]);

  // 清理文件内容
  const clearContentForFile = useCallback((videoPath: string) => {
    setFileMediaContents(prev => {
      const newMap = new Map(prev);
      newMap.delete(videoPath);
      return newMap;
    });
  }, []);

  // 清理所有内容
  const clearAllContent = useCallback(() => {
    setFileMediaContents(new Map());
  }, []);

  return {
    fileMediaContents,
    addScreenshot,
    removeScreenshot,
    getScreenshotsForFile,
    addClip,
    removeClip,
    getClipsForFile,
    createGroup,
    removeGroup,
    getGroupsForFile,
    getGroupById,
    addMultiVideoScreenshots,
    addMultiVideoClips,
    clearContentForFile,
    clearAllContent,
  };
};
