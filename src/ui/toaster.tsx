// 简化的 Toaster 组件，避免 Chakra UI v3 兼容性问题
import React, { useState, useEffect } from 'react';

// 通知状态管理
let toastId = 0;
const toastListeners: Array<(toasts: ToastItem[]) => void> = [];
let currentToasts: ToastItem[] = [];

interface ToastItem {
  id: number;
  description: string;
  status: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  timestamp: number;
}

// 添加通知
const addToast = (toast: Omit<ToastItem, 'id' | 'timestamp'>) => {
  const newToast: ToastItem = {
    ...toast,
    id: ++toastId,
    timestamp: Date.now()
  };

  currentToasts = [...currentToasts, newToast];
  toastListeners.forEach(listener => listener(currentToasts));

  // 自动移除
  setTimeout(() => {
    removeToast(newToast.id);
  }, toast.duration || 3000);
};

// 移除通知
const removeToast = (id: number) => {
  currentToasts = currentToasts.filter(toast => toast.id !== id);
  toastListeners.forEach(listener => listener(currentToasts));
};

// 简单的 toaster 实现，兼容 create API
export const toaster = {
  create: (options: { description: string; status: string; duration?: number }) => {
    const { description, status, duration = 3000 } = options;

    // 控制台日志（保持原有功能）
    switch (status) {
      case 'success':
        console.log('✅ Success:', description);
        break;
      case 'error':
        console.error('❌ Error:', description);
        break;
      case 'warning':
        console.warn('⚠️ Warning:', description);
        break;
      case 'info':
      default:
        console.log('ℹ️ Info:', description);
        break;
    }

    // 添加用户可见通知
    addToast({
      description,
      status: status as ToastItem['status'],
      duration
    });
  },
  success: (message: string) => {
    console.log('✅ Success:', message);
    addToast({ description: message, status: 'success', duration: 3000 });
  },
  error: (message: string) => {
    console.error('❌ Error:', message);
    addToast({ description: message, status: 'error', duration: 3000 });
  },
  info: (message: string) => {
    console.log('ℹ️ Info:', message);
    addToast({ description: message, status: 'info', duration: 3000 });
  },
  dismiss: (id?: string) => {
    console.log('Dismiss toast:', id);
    if (id) {
      removeToast(parseInt(id));
    }
  }
};

// 简化的 Toaster 组件 - 显示用户可见通知
export const Toaster = () => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  useEffect(() => {
    // 订阅通知更新
    const listener = (newToasts: ToastItem[]) => {
      setToasts(newToasts);
    };

    toastListeners.push(listener);

    // 清理订阅
    return () => {
      const index = toastListeners.indexOf(listener);
      if (index > -1) {
        toastListeners.splice(index, 1);
      }
    };
  }, []);

  if (toasts.length === 0) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: 9999,
      display: 'flex',
      flexDirection: 'column',
      gap: '8px'
    }}>
      {toasts.map((toast) => (
        <div
          key={toast.id}
          style={{
            padding: '12px 16px',
            borderRadius: '6px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '500',
            minWidth: '200px',
            maxWidth: '400px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            backgroundColor: getToastColor(toast.status),
            animation: 'slideIn 0.3s ease-out',
            cursor: 'pointer'
          }}
          onClick={() => removeToast(toast.id)}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>{getToastIcon(toast.status)}</span>
            <span>{toast.description}</span>
          </div>
        </div>
      ))}
      <style>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

// 获取通知颜色
const getToastColor = (status: ToastItem['status']): string => {
  switch (status) {
    case 'success': return '#10B981'; // 绿色
    case 'error': return '#EF4444';   // 红色
    case 'warning': return '#F59E0B'; // 橙色
    case 'info': return '#3B82F6';    // 蓝色
    default: return '#6B7280';        // 灰色
  }
};

// 获取通知图标
const getToastIcon = (status: ToastItem['status']): string => {
  switch (status) {
    case 'success': return '✅';
    case 'error': return '❌';
    case 'warning': return '⚠️';
    case 'info': return 'ℹ️';
    default: return '📝';
  }
};
