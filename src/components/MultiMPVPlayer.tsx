import React, { useRef, useEffect, useState, useImperativeHandle, forwardRef, useCallback } from 'react';
import { Box, Grid, Text } from '@chakra-ui/react';
import { VideoFile } from '../types/electron';
import MPVPlayer, { MPVPlayerRef } from './MPVPlayer';

// 保持与 MultiVideoPlayer 相同的接口
export interface MultiMPVPlayerRef {
  seek: (time: number) => void;
  volumeChange: (volume: number) => void;
  toggleMute: () => void;
  skipTime: (seconds: number) => void;
  play: () => void;
  pause: () => void;
  togglePlay: () => void;
  toggleFullscreen: () => void;
  // 缩放控制
  toggleZoom?: () => void;
  setScale?: (scale: number) => void;
  resetZoom?: () => void;
  // 视频显示控制
  toggleVideoVisibility?: (index: number) => void;
  returnToMultiVideoLayout: () => void;
  // 获取当前视频顺序
  getVideoOrder?: () => VideoFile[];
}

interface MultiMPVPlayerProps {
  videoFiles: VideoFile[];
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onClose?: () => void;
  onSingleVideoPlay?: (file: VideoFile) => void;
  // 控制函数
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlay?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number) => void;
  onToggleMute?: () => void;
  onSkipTime?: (seconds: number) => void;
  // 状态
  isPlaying?: boolean;
  masterTime?: number;
  masterDuration?: number;
  volume?: number;
  isMuted?: boolean;
  // 其他属性（保持兼容性）
  isSingleVideoMode?: boolean;
  showReturnToMultiVideo?: boolean;
  onReturnToMultiVideo?: () => void;
  onMainVideoFocusChange?: (isFocused: boolean) => void;
  onVisibleVideoCountChange?: (count: number) => void;
  onZoomStateChange?: (isZoomed: boolean) => void;
  videoVisibility?: boolean[];
  onVideoVisibilityChange?: (visibility: boolean[]) => void;
  isClipping?: boolean;
  clipProgress?: number;
}

const MultiMPVPlayer = forwardRef<MultiMPVPlayerRef, MultiMPVPlayerProps>(({
  videoFiles,
  onTimeUpdate,
  onClose,
  onSingleVideoPlay,
  onPlay,
  onPause,
  onTogglePlay,
  onSeek,
  onVolumeChange,
  onToggleMute,
  onSkipTime,
  isPlaying = false,
  masterTime = 0,
  masterDuration = 0,
  volume = 1,
  isMuted = false,
  isSingleVideoMode = false,
  showReturnToMultiVideo = false,
  onReturnToMultiVideo,
  onMainVideoFocusChange,
  onVisibleVideoCountChange,
  onZoomStateChange,
  videoVisibility = [],
  onVideoVisibilityChange,
  isClipping = false,
  clipProgress = 0
}, ref) => {
  const mpvPlayerRefs = useRef<Map<string, MPVPlayerRef>>(new Map());
  const containerRef = useRef<HTMLDivElement>(null);
  const [mpvReady, setMpvReady] = useState<Map<string, boolean>>(new Map());
  const [currentMasterTime, setCurrentMasterTime] = useState(masterTime);
  const [currentMasterDuration, setCurrentMasterDuration] = useState(masterDuration);

  // 获取可见的视频文件
  const visibleVideoFiles = videoFiles.filter((_, index) =>
    videoVisibility.length === 0 || videoVisibility[index] !== false
  );

  // 计算网格布局
  const getGridLayout = useCallback(() => {
    const count = visibleVideoFiles.length;
    if (count === 0) return { columns: 1, rows: 1 };
    if (count === 1) return { columns: 1, rows: 1 };
    if (count === 2) return { columns: 2, rows: 1 };
    if (count <= 4) return { columns: 2, rows: 2 };
    if (count <= 6) return { columns: 3, rows: 2 };
    if (count <= 9) return { columns: 3, rows: 3 };
    return { columns: 4, rows: Math.ceil(count / 4) };
  }, [visibleVideoFiles.length]);

  // 播放控制方法
  const play = useCallback(async () => {
    try {
      const promises = Array.from(mpvPlayerRefs.current.values()).map(playerRef =>
        playerRef.play().catch(console.error)
      );
      await Promise.all(promises);
      onPlay?.();
    } catch (error) {
      console.error('多视频播放失败:', error);
    }
  }, [onPlay]);

  const pause = useCallback(async () => {
    try {
      const promises = Array.from(mpvPlayerRefs.current.values()).map(playerRef =>
        playerRef.pause().catch(console.error)
      );
      await Promise.all(promises);
      onPause?.();
    } catch (error) {
      console.error('多视频暂停失败:', error);
    }
  }, [onPause]);

  const togglePlay = useCallback(() => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
    onTogglePlay?.();
  }, [isPlaying, play, pause, onTogglePlay]);

  const seek = useCallback(async (time: number) => {
    try {
      const promises = Array.from(mpvPlayerRefs.current.values()).map(playerRef =>
        playerRef.seek(time).catch(console.error)
      );
      await Promise.all(promises);
      setCurrentMasterTime(time);
      onSeek?.(time);
    } catch (error) {
      console.error('多视频跳转失败:', error);
    }
  }, [onSeek]);

  const volumeChange = useCallback(async (newVolume: number) => {
    try {
      const promises = Array.from(mpvPlayerRefs.current.values()).map(playerRef =>
        playerRef.setVolume(newVolume).catch(console.error)
      );
      await Promise.all(promises);
      onVolumeChange?.(newVolume);
    } catch (error) {
      console.error('多视频音量调节失败:', error);
    }
  }, [onVolumeChange]);

  const toggleMute = useCallback(async () => {
    try {
      const promises = Array.from(mpvPlayerRefs.current.values()).map(playerRef =>
        playerRef.setMute(!isMuted).catch(console.error)
      );
      await Promise.all(promises);
      onToggleMute?.();
    } catch (error) {
      console.error('多视频静音切换失败:', error);
    }
  }, [isMuted, onToggleMute]);

  const skipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(masterDuration, currentMasterTime + seconds));
    seek(newTime);
    onSkipTime?.(seconds);
  }, [currentMasterTime, masterDuration, seek, onSkipTime]);

  // 处理单个视频的时间更新
  const handleVideoTimeUpdate = useCallback((videoId: string, currentTime: number, duration: number) => {
    // 使用第一个视频作为主时间源
    if (videoId === visibleVideoFiles[0]?.id) {
      setCurrentMasterTime(currentTime);
      setCurrentMasterDuration(duration);
      onTimeUpdate?.(currentTime, duration);
    }
  }, [visibleVideoFiles, onTimeUpdate]);

  // 处理视频准备就绪
  const handleVideoReady = useCallback((videoId: string) => {
    setMpvReady(prev => new Map(prev).set(videoId, true));
    console.log(`视频 ${videoId} 准备就绪`);
  }, []);

  // 处理视频错误
  const handleVideoError = useCallback((videoId: string, error: string) => {
    console.error(`视频 ${videoId} 错误:`, error);
    setMpvReady(prev => {
      const newMap = new Map(prev);
      newMap.delete(videoId);
      return newMap;
    });
  }, []);

  // 暴露控制方法给外部
  useImperativeHandle(ref, () => ({
    seek,
    volumeChange,
    toggleMute,
    skipTime,
    play,
    pause,
    togglePlay,
    toggleFullscreen: () => {
      // TODO: 实现全屏功能
      console.log('全屏功能待实现');
    },
    toggleZoom: () => {
      // TODO: 实现缩放功能
      console.log('缩放功能待实现');
    },
    setScale: (scale: number) => {
      // TODO: 实现缩放设置
      console.log('设置缩放:', scale);
    },
    resetZoom: () => {
      // TODO: 实现重置缩放
      console.log('重置缩放');
    },
    toggleVideoVisibility: (index: number) => {
      // TODO: 实现视频显示切换
      console.log('切换视频显示:', index);
    },
    returnToMultiVideoLayout: () => {
      onReturnToMultiVideo?.();
    },
    getVideoOrder: () => visibleVideoFiles
  }), [seek, volumeChange, toggleMute, skipTime, play, pause, togglePlay, onReturnToMultiVideo, visibleVideoFiles]);

  // 监听外部时间变化
  useEffect(() => {
    if (Math.abs(masterTime - currentMasterTime) > 1) {
      seek(masterTime);
    }
  }, [masterTime, currentMasterTime, seek]);

  const gridLayout = getGridLayout();

  return (
    <Box
      ref={containerRef}
      width="100%"
      height="100%"
      bg="black"
      position="relative"
      overflow="hidden"
    >
      {visibleVideoFiles.length === 0 ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="100%"
          color="gray.400"
          fontSize="lg"
        >
          请选择视频文件
        </Box>
      ) : (
        <Grid
          templateColumns={`repeat(${gridLayout.columns}, 1fr)`}
          templateRows={`repeat(${gridLayout.rows}, 1fr)`}
          width="100%"
          height="100%"
          gap={1}
        >
          {visibleVideoFiles.map((videoFile, index) => (
            <Box
              key={videoFile.id}
              position="relative"
              overflow="hidden"
              bg="black"
            >
              <MPVPlayer
                ref={(playerRef) => {
                  if (playerRef) {
                    mpvPlayerRefs.current.set(videoFile.id, playerRef);
                  } else {
                    mpvPlayerRefs.current.delete(videoFile.id);
                  }
                }}
                videoFile={videoFile}
                isPlaying={isPlaying}
                currentTime={currentMasterTime}
                volume={volume}
                isMuted={isMuted}
                onTimeUpdate={(time, duration) => handleVideoTimeUpdate(videoFile.id, time, duration)}
                onReady={() => handleVideoReady(videoFile.id)}
                onError={(error) => handleVideoError(videoFile.id, error)}
              />

              {/* 视频标签 */}
              <Box
                position="absolute"
                top={2}
                left={2}
                bg="rgba(0,0,0,0.7)"
                color="white"
                fontSize="xs"
                px={2}
                py={1}
                borderRadius="md"
                zIndex={10}
              >
                {videoFile.name}
              </Box>
            </Box>
          ))}
        </Grid>
      )}
    </Box>
  );
});

MultiMPVPlayer.displayName = 'MultiMPVPlayer';

export default MultiMPVPlayer;