import React, { useState, useCallback } from 'react';
import { Button } from '@chakra-ui/react';
import { FiCamera } from 'react-icons/fi';
import { toaster } from '../ui/toaster';
import ScreenshotSaveDialog from './ScreenshotSaveDialog';
import { useScreenshots } from '../hooks/useScreenshots';
import { useFileMediaContent } from '../hooks/useFileMediaContent';
import {
  generateScreenshotFilename,
  saveBase64ImageWithDialog
} from '../utils/screenshotUtils';

export interface ClipControlsProps {
  videoPath: string;
  currentTime: number;
  onScreenshotTaken?: (screenshot: any) => void;
  isMultiVideo?: boolean; // 是否为多视频模式
  onCaptureFrame?: () => Promise<any>; // 自定义截图处理函数
  videoFiles?: any[]; // 多视频文件列表（用于保存时重新截图）
  videoVisibility?: boolean[]; // 视频可见性状态
}

const ClipControls: React.FC<ClipControlsProps> = ({
  videoPath,
  currentTime,
  onScreenshotTaken,
  isMultiVideo = false,
  onCaptureFrame,
  videoFiles = [],
  videoVisibility = []
}) => {
  // 截图相关状态
  const [screenshotDialogOpen, setScreenshotDialogOpen] = useState(false);
  const [currentScreenshotData, setCurrentScreenshotData] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { addScreenshot } = useScreenshots();
  const { addScreenshot: addFileScreenshot } = useFileMediaContent();

  // 处理截图 - 先创建预览，然后用户确认保存
  const handleCaptureFrame = useCallback(async () => {
    try {
      setIsProcessing(true);

      // 检查是否有可见的视频
      if (isMultiVideo && videoVisibility.length > 0 && !videoVisibility.some(visible => visible)) {
        toaster.create({
          description: '没有可见的视频可以截图',
          status: 'warning',
          duration: 2000,
        });
        return;
      }

      if (isMultiVideo && onCaptureFrame) {
        // 多视频模式：使用自定义截图处理函数
        const result = await onCaptureFrame();

        if (result?.isMultiVideo && result?.compositeScreenshot) {
          // 设置多视频截图数据并打开预览对话框
          setCurrentScreenshotData({
            isMultiVideo: true,
            compositeScreenshot: result.compositeScreenshot,
            timestamp: result.timestamp
          });
          setScreenshotDialogOpen(true);
        } else if (result === null) {
          // 用户取消了截图操作，不显示错误
          return;
        } else {
          throw new Error('截图失败');
        }
      } else {
        // 单视频模式：获取base64预览数据
        const result = await (window as any).electronAPI?.captureFrame?.(videoPath, currentTime, {
          returnBase64: true, // 返回base64数据而不是创建临时文件
          width: 3840, // 使用原视频分辨率或更高分辨率
          height: 2160,
          quality: 95 // 高质量截图
        });

        if (result?.success) {
          // 设置截图数据并打开预览对话框
          setCurrentScreenshotData({
            base64Data: result.base64,
            timestamp: currentTime,
            videoName: result.videoName || 'Unknown Video',
            resolution: result.resolution
          });
          setScreenshotDialogOpen(true);
        } else {
          throw new Error(result?.error || '截图失败');
        }
      }
    } catch (error) {
      console.error('截图失败:', error);
      toaster.create({
        description: '截图失败',
        status: 'error',
        duration: 2000,
      });
    } finally {
      setIsProcessing(false);
    }
  }, [videoPath, currentTime]);

  // 保存截图 - 用户确认后使用base64数据保存
  const handleSaveScreenshot = useCallback(async () => {
    if (!currentScreenshotData) return;

    try {
      let base64Data: string;
      let filename: string;
      let videoName: string;

      if (currentScreenshotData.isMultiVideo && currentScreenshotData.compositeScreenshot) {
        // 多视频截图保存
        base64Data = currentScreenshotData.compositeScreenshot.base64Data!;
        videoName = '多视频合成';
        filename = generateScreenshotFilename(videoName, currentTime, true);
      } else {
        // 单视频截图保存
        base64Data = currentScreenshotData.base64Data!;
        videoName = currentScreenshotData.videoName || 'video';
        filename = generateScreenshotFilename(videoName, currentTime, false);
      }

      // 使用base64数据保存图片
      const saved = await saveBase64ImageWithDialog(base64Data, filename);

      if (saved) {
        // 添加到截图管理（注意：这里我们没有实际的文件路径，所以使用base64数据）
        const screenshot = {
          videoPath: currentScreenshotData.isMultiVideo ? 'multi-video' : videoPath,
          timestamp: currentTime,
          imagePath: '', // 由于是base64保存，没有固定路径
          thumbnailPath: '' // 同样没有缩略图路径
        };

        addScreenshot(screenshot);
        // 同时添加到新的媒体内容管理系统
        addFileScreenshot({
          videoPath: currentScreenshotData.isMultiVideo ? 'multi-video' : videoPath,
          timestamp: currentTime,
          imagePath: '',
          thumbnailPath: ''
        });
        onScreenshotTaken?.(screenshot);

        toaster.create({
          description: '已保存',
          status: 'success',
          duration: 2000,
        });

        // 关闭预览对话框
        setScreenshotDialogOpen(false);
        setCurrentScreenshotData(null);
      }
      // 如果用户取消保存，不关闭对话框
    } catch (error) {
      console.error('保存截图失败:', error);
      toaster.create({
        description: '保存失败',
        status: 'error',
        duration: 2000,
      });
    }
  }, [videoPath, currentTime, currentScreenshotData, addScreenshot, addFileScreenshot, onScreenshotTaken]);

  return (
    <>
      {/* 截图按钮 */}
      <Button
        size="sm"
        variant="ghost"
        color="white"
        onClick={handleCaptureFrame}
        loading={isProcessing}
        title="截取当前帧"
        _hover={{ bg: "rgba(255,255,255,0.2)" }}
      >
        <FiCamera />
      </Button>

      {/* 截图预览保存对话框 */}
      <ScreenshotSaveDialog
        open={screenshotDialogOpen}
        onOpenChange={(details) => setScreenshotDialogOpen(details.open)}
        screenshotData={currentScreenshotData}
        onSave={handleSaveScreenshot}
      />
    </>
  );
};

export default ClipControls;
