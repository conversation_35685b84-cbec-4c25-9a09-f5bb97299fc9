import React, { useRef, useEffect, useState, useImperativeHandle, forwardRef, useCallback } from 'react';
import { Box, Text, Spinner } from '@chakra-ui/react';
import { VideoFile } from '../types/electron';

export interface MPVPlayerRef {
  play: () => Promise<void>;
  pause: () => Promise<void>;
  seek: (time: number) => Promise<void>;
  setVolume: (volume: number) => Promise<void>;
  setMute: (muted: boolean) => Promise<void>;
  getCurrentTime: () => Promise<number>;
  getDuration: () => Promise<number>;
  getPlaybackState: () => Promise<any>;
}

interface MPVPlayerProps {
  videoFile: VideoFile;
  isPlaying?: boolean;
  currentTime?: number;
  volume?: number;
  isMuted?: boolean;
  onTimeUpdate?: (time: number, duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onError?: (error: string) => void;
  onReady?: () => void;
  style?: React.CSSProperties;
  className?: string;
}

const MPVPlayer = forwardRef<MPVPlayerRef, MPVPlayerProps>(({
  videoFile,
  isPlaying = false,
  currentTime = 0,
  volume = 1,
  isMuted = false,
  onTimeUpdate,
  onPlay,
  onPause,
  onError,
  onReady,
  style,
  className
}, ref) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mpvReady, setMpvReady] = useState(false);
  const [mpvError, setMpvError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const [windowHandle, setWindowHandle] = useState<number | string | null>(null);
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化 MPV
  const initializeMPV = useCallback(async () => {
    if (isInitializing || mpvReady) {
      return;
    }

    setIsInitializing(true);
    setMpvError(null);

    try {
      console.log('🎬 初始化 MPV 播放器:', videoFile.name);

      // 获取窗口句柄
      const handleResult = await window.electronAPI.getWindowHandle();
      if (!handleResult.success) {
        throw new Error(`获取窗口句柄失败: ${handleResult.error}`);
      }

      setWindowHandle(handleResult.windowHandle);
      console.log('🪟 窗口句柄获取成功:', handleResult.windowHandle);

      // 启动 MPV 进程
      const startResult = await window.electronAPI.startMPV(
        videoFile.id,
        videoFile.path,
        handleResult.windowHandle,
        {
          // MPV 启动选项
          'video-sync': 'display-resample',
          'interpolation': 'yes',
          'tscale': 'oversample'
        }
      );

      if (!startResult.success) {
        throw new Error(`启动 MPV 失败: ${startResult.error}`);
      }

      console.log('✅ MPV 进程启动成功:', startResult.processId);

      // 等待 MPV 准备就绪
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMpvReady(true);
      setIsInitializing(false);
      onReady?.();

      // 启动时间更新定时器
      startTimeUpdateTimer();

      console.log('🎉 MPV 播放器初始化完成');

    } catch (error) {
      console.error('❌ MPV 初始化失败:', error);
      setMpvError(error instanceof Error ? error.message : '未知错误');
      setIsInitializing(false);
      onError?.(error instanceof Error ? error.message : '未知错误');
    }
  }, [videoFile, isInitializing, mpvReady, onReady, onError]);

  // 清理 MPV
  const cleanupMPV = useCallback(async () => {
    if (!mpvReady) {
      return;
    }

    try {
      console.log('🧹 清理 MPV 播放器:', videoFile.id);

      // 停止时间更新定时器
      stopTimeUpdateTimer();

      // 停止 MPV 进程
      await window.electronAPI.stopMPV(videoFile.id);

      setMpvReady(false);
      setWindowHandle(null);

      console.log('✅ MPV 播放器清理完成');

    } catch (error) {
      console.error('❌ MPV 清理失败:', error);
    }
  }, [videoFile.id, mpvReady]);

  // 启动时间更新定时器
  const startTimeUpdateTimer = useCallback(() => {
    if (timeUpdateIntervalRef.current) {
      return;
    }

    timeUpdateIntervalRef.current = setInterval(async () => {
      if (!mpvReady) {
        return;
      }

      try {
        const stateResult = await window.electronAPI.mpvGetPlaybackState(videoFile.id);
        if (stateResult.success && stateResult.state) {
          const { currentTime, duration } = stateResult.state;
          onTimeUpdate?.(currentTime, duration);
        }
      } catch (error) {
        // 静默处理时间更新错误
      }
    }, 100); // 每100ms更新一次
  }, [mpvReady, videoFile.id, onTimeUpdate]);

  // 停止时间更新定时器
  const stopTimeUpdateTimer = useCallback(() => {
    if (timeUpdateIntervalRef.current) {
      clearInterval(timeUpdateIntervalRef.current);
      timeUpdateIntervalRef.current = null;
    }
  }, []);

  // 播放控制方法
  const play = useCallback(async () => {
    if (!mpvReady) {
      throw new Error('MPV 未准备就绪');
    }

    const result = await window.electronAPI.mpvPlay(videoFile.id);
    if (!result.success) {
      throw new Error(result.error || '播放失败');
    }

    onPlay?.();
  }, [mpvReady, videoFile.id, onPlay]);

  const pause = useCallback(async () => {
    if (!mpvReady) {
      throw new Error('MPV 未准备就绪');
    }

    const result = await window.electronAPI.mpvPause(videoFile.id);
    if (!result.success) {
      throw new Error(result.error || '暂停失败');
    }

    onPause?.();
  }, [mpvReady, videoFile.id, onPause]);

  const seek = useCallback(async (time: number) => {
    if (!mpvReady) {
      throw new Error('MPV 未准备就绪');
    }

    const result = await window.electronAPI.mpvSeek(videoFile.id, time);
    if (!result.success) {
      throw new Error(result.error || '跳转失败');
    }
  }, [mpvReady, videoFile.id]);

  const setVolume = useCallback(async (volume: number) => {
    if (!mpvReady) {
      throw new Error('MPV 未准备就绪');
    }

    const result = await window.electronAPI.mpvSetVolume(videoFile.id, volume * 100);
    if (!result.success) {
      throw new Error(result.error || '设置音量失败');
    }
  }, [mpvReady, videoFile.id]);

  const setMute = useCallback(async (muted: boolean) => {
    if (!mpvReady) {
      throw new Error('MPV 未准备就绪');
    }

    const result = await window.electronAPI.mpvSetMute(videoFile.id, muted);
    if (!result.success) {
      throw new Error(result.error || '设置静音失败');
    }
  }, [mpvReady, videoFile.id]);

  const getCurrentTime = useCallback(async (): Promise<number> => {
    if (!mpvReady) {
      return 0;
    }

    const result = await window.electronAPI.mpvGetCurrentTime(videoFile.id);
    return result.success ? result.currentTime : 0;
  }, [mpvReady, videoFile.id]);

  const getDuration = useCallback(async (): Promise<number> => {
    if (!mpvReady) {
      return 0;
    }

    const result = await window.electronAPI.mpvGetDuration(videoFile.id);
    return result.success ? result.duration : 0;
  }, [mpvReady, videoFile.id]);

  const getPlaybackState = useCallback(async () => {
    if (!mpvReady) {
      return null;
    }

    const result = await window.electronAPI.mpvGetPlaybackState(videoFile.id);
    return result.success ? result.state : null;
  }, [mpvReady, videoFile.id]);

  // 暴露控制方法给外部
  useImperativeHandle(ref, () => ({
    play,
    pause,
    seek,
    setVolume,
    setMute,
    getCurrentTime,
    getDuration,
    getPlaybackState
  }), [play, pause, seek, setVolume, setMute, getCurrentTime, getDuration, getPlaybackState]);

  // 组件挂载时初始化 MPV
  useEffect(() => {
    if (videoFile && containerRef.current) {
      initializeMPV();
    }

    return () => {
      cleanupMPV();
    };
  }, [videoFile, initializeMPV, cleanupMPV]);

  // 监听外部播放状态变化
  useEffect(() => {
    if (!mpvReady) {
      return;
    }

    const syncPlayState = async () => {
      try {
        const state = await getPlaybackState();
        if (state && state.isPaused !== !isPlaying) {
          if (isPlaying) {
            await play();
          } else {
            await pause();
          }
        }
      } catch (error) {
        console.error('同步播放状态失败:', error);
      }
    };

    syncPlayState();
  }, [isPlaying, mpvReady, play, pause, getPlaybackState]);

  // 监听外部音量变化
  useEffect(() => {
    if (!mpvReady) {
      return;
    }

    const syncVolume = async () => {
      try {
        await setVolume(volume);
      } catch (error) {
        console.error('同步音量失败:', error);
      }
    };

    syncVolume();
  }, [volume, mpvReady, setVolume]);

  // 监听外部静音状态变化
  useEffect(() => {
    if (!mpvReady) {
      return;
    }

    const syncMute = async () => {
      try {
        await setMute(isMuted);
      } catch (error) {
        console.error('同步静音状态失败:', error);
      }
    };

    syncMute();
  }, [isMuted, mpvReady, setMute]);

  // 监听外部时间变化（跳转）
  useEffect(() => {
    if (!mpvReady) {
      return;
    }

    const syncTime = async () => {
      try {
        const currentMPVTime = await getCurrentTime();
        const timeDiff = Math.abs(currentMPVTime - currentTime);

        // 如果时间差超过1秒，执行跳转
        if (timeDiff > 1) {
          await seek(currentTime);
        }
      } catch (error) {
        console.error('同步时间失败:', error);
      }
    };

    syncTime();
  }, [currentTime, mpvReady, seek, getCurrentTime]);

  return (
    <Box
      ref={containerRef}
      width="100%"
      height="100%"
      bg="black"
      position="relative"
      overflow="hidden"
      display="flex"
      alignItems="center"
      justifyContent="center"
      style={style}
      className={className}
    >
      {/* 加载状态 */}
      {isInitializing && (
        <Box
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={3}
          color="white"
          zIndex={10}
        >
          <Spinner size="lg" color="blue.400" />
          <Text fontSize="sm" opacity={0.8}>
            正在初始化 MPV 播放器...
          </Text>
        </Box>
      )}

      {/* 错误状态 */}
      {mpvError && (
        <Box
          position="absolute"
          top="50%"
          left="50%"
          transform="translate(-50%, -50%)"
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={3}
          color="red.400"
          textAlign="center"
          maxW="80%"
          zIndex={10}
        >
          <Text fontSize="lg" fontWeight="bold">
            播放器初始化失败
          </Text>
          <Text fontSize="sm" opacity={0.8}>
            {mpvError}
          </Text>
          <Text fontSize="xs" opacity={0.6}>
            请检查 MPV 是否正确安装
          </Text>
        </Box>
      )}

      {/* MPV 嵌入容器 */}
      {!isInitializing && !mpvError && (
        <Box
          id={`mpv-container-${videoFile.id}`}
          width="100%"
          height="100%"
          bg="black"
          position="relative"
        >
          {/* 视频信息覆盖层（仅在未准备就绪时显示） */}
          {!mpvReady && (
            <Box
              position="absolute"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              color="white"
              textAlign="center"
              zIndex={5}
            >
              <Text fontSize="sm" opacity={0.7}>
                {videoFile.name}
              </Text>
              <Text fontSize="xs" opacity={0.5} mt={1}>
                等待 MPV 准备就绪...
              </Text>
            </Box>
          )}
        </Box>
      )}

      {/* 调试信息（仅在开发模式下显示） */}
      {process.env.NODE_ENV === 'development' && (
        <Box
          position="absolute"
          top={2}
          left={2}
          bg="rgba(0,0,0,0.7)"
          color="white"
          fontSize="xs"
          p={2}
          borderRadius="md"
          zIndex={20}
        >
          <Text>视频ID: {videoFile.id}</Text>
          <Text>窗口句柄: {windowHandle}</Text>
          <Text>MPV状态: {mpvReady ? '就绪' : '未就绪'}</Text>
          <Text>初始化中: {isInitializing ? '是' : '否'}</Text>
        </Box>
      )}
    </Box>
  );
});

MPVPlayer.displayName = 'MPVPlayer';

export default MPVPlayer;