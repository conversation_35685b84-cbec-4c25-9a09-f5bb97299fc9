import React from 'react';
import ReactDOM from 'react-dom/client';
import { ChakraProvider } from '@chakra-ui/react';
import App from './App';
import theme from './theme';
import { Toaster } from './ui/toaster'; // 使用简化版本

// 添加详细的错误调试
console.log('🔍 [MAIN] 开始应用初始化...');
console.log('🔍 [MAIN] React 版本:', React.version);
console.log('🔍 [MAIN] React 对象:', Object.keys(React));
console.log('🔍 [MAIN] React.Children:', typeof React.Children);

// 检查 React.Children 是否存在
if (!React.Children) {
  console.error('❌ [MAIN] React.Children 不存在！');
} else {
  console.log('✅ [MAIN] React.Children 存在:', Object.keys(React.Children));
}

// 添加错误边界
class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    console.error('🚨 [ERROR BOUNDARY] 捕获到错误:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('🚨 [ERROR BOUNDARY] 错误详情:', error, errorInfo);
  }

  render() {
    if ((this.state as any).hasError) {
      return (
        <div style={{ padding: '20px', color: 'red', fontFamily: 'monospace' }}>
          <h2>应用启动错误</h2>
          <p>错误信息: {(this.state as any).error?.toString()}</p>
          <p>请检查开发者工具控制台获取详细信息</p>
        </div>
      );
    }
    return (this.props as any).children;
  }
}

try {
  console.log('🔍 [MAIN] 创建 React 根元素...');
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  console.log('🔍 [MAIN] 根元素创建成功，开始渲染...');

  root.render(
    <ErrorBoundary>
      <ChakraProvider value={theme}>
        <App />
        <Toaster />
      </ChakraProvider>
    </ErrorBoundary>
  );

  console.log('✅ [MAIN] 应用渲染完成');
} catch (error) {
  console.error('❌ [MAIN] 应用初始化失败:', error);
}
