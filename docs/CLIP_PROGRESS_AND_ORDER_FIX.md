# 剪辑进度显示和视频顺序修复

## 🎯 问题描述

1. **剪辑进度显示问题**：在进行视频剪辑时，没能正确展示剪辑进度
2. **视频顺序问题**：剪辑时没有按照用户看到的布局进行排列合成

## 🔍 问题分析

### 1. **剪辑进度显示问题**

#### 根本原因：
- App.tsx 中监听的是 `video:clipProgress` 事件
- 但是剪辑功能发送的是 `video:clipMultiVideoProgress` 事件
- 事件名称不匹配导致进度更新失败

#### 代码问题：
```javascript
// App.tsx - 只监听单视频剪辑进度
(window as any).electronAPI?.onClipProgress?.(handleClipProgress);

// main.js - 发送的是不同的事件名
event.sender.send('video:clipMultiVideoProgress', progress); // ← 不匹配！
```

### 2. **视频顺序问题**

#### 根本原因：
- MultiVideoPlayer 中的剪辑逻辑仍使用旧的 `visibleVideos` 过滤方式
- 没有使用 `orderedVisibleVideos` 来保持用户看到的顺序

#### 代码问题：
```javascript
// MultiVideoPlayer.tsx - 使用错误的过滤方式
const visibleVideos = videoFiles.filter((_, index) => videoVisibility[index]);
// ← 丢失了用户自定义的排列顺序！
```

## ✅ 解决方案

### 1. **修复剪辑进度显示**

#### A. 添加进度监听器
```javascript
// App.tsx - 同时监听两种剪辑进度事件
const handleClipProgress = (event: any, progress: number) => {
  setClipProgress(progress);
};

const handleMultiVideoClipProgress = (event: any, progress: number) => {
  setClipProgress(progress);
};

// 添加两种监听器
(window as any).electronAPI?.onClipProgress?.(handleClipProgress);
(window as any).electronAPI?.onMultiVideoClipProgress?.(handleMultiVideoClipProgress);
```

#### B. 扩展 preload.js API
```javascript
// preload.js - 添加剪辑进度监听
onMultiVideoClipProgress: (callback) => {
  ipcRenderer.on('video:clipMultiVideoProgress', callback);
},
removeMultiVideoClipProgressListener: (callback) => {
  ipcRenderer.removeListener('video:clipMultiVideoProgress', callback);
},
```

### 2. **修复视频顺序问题**

#### A. MultiVideoPlayer 中的修复
```javascript
// 修复前：使用简单过滤
const visibleVideos = videoFiles.filter((_, index) => videoVisibility[index]);

// 修复后：保持用户看到的顺序
const orderedVisibleVideos = displayFiles
  .map((file, displayIndex) => {
    const originalIndex = videoFiles.findIndex(f => f.id === file.id);
    const isVisible = videoVisibility[originalIndex];
    return isVisible ? {
      ...file,
      displayOrder: displayIndex,
      originalIndex: originalIndex
    } : null;
  })
  .filter(Boolean);
```

#### B. 传递正确的参数
```javascript
// 使用正确的视频列表和顺序标记
const result = await (window as any).electronAPI?.clipMultiVideo?.({
  videoFiles: orderedVisibleVideos, // 按显示顺序排列的可见视频
  startTime: clipStartTime,
  endTime: clipEndTime,
  showSaveDialog: true,
  preserveOrder: true // 标记保持传入的顺序
});
```

## 📦 修复版本信息

- **修复时间**: 2025-08-03 01:30
- **修复内容**: 剪辑进度显示和视频顺序
- **影响范围**: App.tsx, MultiVideoPlayer.tsx, preload.js

## 🎨 修复效果

### ✅ **剪辑进度正确显示**
```
┌─────────────────────────────────┐
│        正在剪辑视频...         │
│                                │
│ ████████████░░░░░░░░░░░░░░░░░░ │  ← 进度条正确更新
│              65%               │  ← 百分比实时显示
└─────────────────────────────────┘
```

### ✅ **视频顺序正确合成**
```
用户界面显示：
┌─────────┬─────────┐
│   R后   │   F前   │
├─────────┴─────────┤
│       I内         │
└───────────────────┘

剪辑结果：
┌─────────┬─────────┐
│   R后   │   F前   │  ← 与界面完全一致！
├─────────┴─────────┤
│       I内         │
└───────────────────┘
```

## 🧪 功能验证

### 测试步骤

#### 1. **剪辑进度测试**
```
操作：选择视频，设置剪辑时间段，开始剪辑
预期：
- 显示剪辑进度遮罩
- 进度条从 0% 开始增长
- 百分比实时更新
- 完成时显示 100% 然后关闭
```

#### 2. **视频顺序测试**
```
操作：
1. 导入多个视频（R、I、F）
2. 调整视频位置（如：F、R、I）
3. 隐藏某个视频（如隐藏 I）
4. 执行剪辑

预期：
- 剪辑结果按照 F、R 的顺序合成
- 隐藏的 I 视频不参与合成
- 布局与界面显示完全一致
```

#### 3. **进度监听测试**
```
操作：同时进行单视频和剪辑
预期：
- 单视频剪辑显示进度
- 剪辑也显示进度
- 两种进度互不干扰
```

## 🎯 技术细节

### 进度事件流程
```
1. 用户点击保存剪辑
2. 调用 clipMultiVideo
3. FFmpeg 开始处理，触发 progress 事件
4. main.js 发送 'video:clipMultiVideoProgress' 事件
5. preload.js 监听并转发给前端
6. App.tsx 接收并更新 clipProgress 状态
7. MultiVideoPlayer 显示进度条和百分比
```

### 视频顺序处理流程
```
1. 用户调整视频位置 → displayFiles 更新
2. 用户隐藏/显示视频 → videoVisibility 更新
3. 开始剪辑时构建 orderedVisibleVideos
4. 传递 preserveOrder: true 标记
5. 后端保持传入的视频顺序
6. FFmpeg 按照传入顺序进行合成
```

### 布局配置映射
```javascript
// 现在的映射关系：
orderedVisibleVideos[0] → FFmpeg input[0] → 布局位置0
orderedVisibleVideos[1] → FFmpeg input[1] → 布局位置1
orderedVisibleVideos[2] → FFmpeg input[2] → 布局位置2

// 布局位置对应：
位置0: 上方左侧 或 单独显示
位置1: 上方右侧 或 下方显示  
位置2: 下方全宽显示
```

## 🏆 完整修复状态

### ✅ **已解决的所有问题**
1. React 应用启动错误 ✅
2. 开发者工具无法打开 ✅
3. 快捷键无响应 ✅
4. 函数作用域错误 ✅
5. 临时目录不存在 ✅
6. FFmpeg 命令参数冲突 ✅
7. 视频拼接顺序错误 ✅
8. 视频拼接高度计算错误 ✅
9. Toaster API 兼容性错误 ✅
10. 用户可见通知缺失 ✅
11. 视频保存成功通知缺失 ✅
12. 剪辑进度显示错误 ✅
13. 剪辑视频顺序错误 ✅

### ✅ **完全正常的功能**
1. React 应用完整显示 ✅
2. 开发者工具和快捷键 ✅
3. 视频播放和操作 ✅
4. 单视频截图（带成功通知）✅
5. 截图（正确顺序+正确尺寸+成功通知）✅
6. 剪辑（正确顺序+正确尺寸+正确进度+成功通知）✅
7. 视频可见性控制 ✅
8. 用户自定义排列 ✅
9. 智能高度计算 ✅
10. 完整的用户通知系统 ✅
11. 实时剪辑进度显示 ✅

---

**修复时间**: 2025-08-03 01:30  
**问题类型**: 剪辑进度显示和视频顺序  
**解决方案**: 修复进度监听器和视频顺序逻辑  
**状态**: 剪辑功能完全修复
