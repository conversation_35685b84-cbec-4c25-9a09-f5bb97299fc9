# MPV 播放器集成 - 完整实现指南

## 🎯 项目概述

本项目成功将 HTML5 `<video>` 播放器替换为 MPV 嵌入式播放器，实现了更强大的视频播放能力和更好的性能表现。

## ✨ 主要特性

### 🎬 播放器功能
- **多格式支持**: 支持 MP4、AVI、MOV、MKV 等多种视频格式
- **高性能播放**: 利用 MPV 的硬件加速能力
- **精准控制**: 精确到秒的进度控制和跳转
- **多视频同步**: 支持多个视频同时播放并保持同步

### 🖥️ 窗口集成
- **无缝嵌入**: 使用 MPV 的 `--wid` 参数将视频嵌入 Electron 窗口
- **跨平台支持**: 兼容 Windows、macOS、Linux 三大平台
- **多架构支持**: 支持 x64 和 ARM64 架构

### 🎛️ 用户界面
- **保持一致**: 保留原有的 HTML 控制界面
- **响应流畅**: 进度条拖拽跟手流畅，响应及时
- **视觉反馈**: 完整的加载状态和错误提示

## 🏗️ 架构设计

### 核心组件

```
electron/
├── mpv-manager.js          # MPV 进程管理器
├── mpv-controller.js       # MPV 控制接口
├── window-handle.js        # 窗口句柄管理
└── multi-mpv-sync.js       # 多视频同步管理

src/components/
├── MPVPlayer.tsx           # 单个 MPV 播放器组件
├── MultiMPVPlayer.tsx      # 多 MPV 播放器组件
└── VideoPlayerControls.tsx # 播放控制界面（已更新）
```

### 数据流

```
用户操作 → VideoPlayerControls → IPC → MPV Manager → MPV Process
                ↓                                        ↓
        UI 状态更新 ← IPC Response ← MPV Controller ← JSON IPC
```

## 🚀 快速开始

### 1. 安装 MPV 二进制文件

```bash
# 自动下载和配置 MPV
npm run setup:mpv
```

### 2. 启动开发环境

```bash
npm run dev
```

### 3. 测试功能

1. 打开应用
2. 选择视频文件
3. 验证播放、暂停、跳转等功能
4. 测试多视频同步播放

## 📦 打包部署

### 构建配置

MPV 二进制文件已配置为 `extraResources`，会自动打包到应用中：

```json
{
  "extraResources": [
    {
      "from": "mpv",
      "to": "mpv",
      "filter": ["**/*"]
    }
  ]
}
```

### 平台特定配置

- **Windows**: `mpv/win-x64/mpv.exe`, `mpv/win-arm64/mpv.exe`
- **macOS**: `mpv/mac-x64/mpv`, `mpv/mac-arm64/mpv`
- **Linux**: `mpv/linux-x64/mpv`, `mpv/linux-arm64/mpv`

### 构建命令

```bash
# Windows
npm run build:windows

# macOS
npm run build:macos

# Linux
npm run build:linux

# 全平台
npm run build:all
```

## 🔧 配置选项

### MPV 启动参数

```javascript
const mpvOptions = {
  '--no-terminal': true,           // 不显示终端
  '--idle': 'yes',                 // 保持空闲状态
  '--no-osc': true,                // 禁用内置控制界面
  '--no-input-default-bindings': true, // 禁用默认键盘绑定
  '--wid': windowHandle,           // 嵌入窗口句柄
  '--keep-open': 'yes',            // 播放结束后保持打开
  '--pause': 'yes',                // 启动时暂停
  '--volume': 100,                 // 默认音量
  '--mute': 'no'                   // 默认不静音
};
```

### 同步参数

```javascript
const syncOptions = {
  syncIntervalMs: 100,      // 同步间隔（毫秒）
  timeTolerance: 0.1,       // 时间容差（秒）
  maxReconnectAttempts: 5,  // 最大重连次数
  reconnectDelay: 1000      // 重连延迟（毫秒）
};
```

## 🎮 API 参考

### MPV 播放器组件

```typescript
interface MPVPlayerProps {
  videoFile: VideoFile;
  isPlaying?: boolean;
  currentTime?: number;
  volume?: number;
  isMuted?: boolean;
  onTimeUpdate?: (time: number, duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onError?: (error: string) => void;
  onReady?: () => void;
}
```

### 控制方法

```typescript
interface MPVPlayerRef {
  play: () => Promise<void>;
  pause: () => Promise<void>;
  seek: (time: number) => Promise<void>;
  setVolume: (volume: number) => Promise<void>;
  setMute: (muted: boolean) => Promise<void>;
  getCurrentTime: () => Promise<number>;
  getDuration: () => Promise<number>;
  getPlaybackState: () => Promise<any>;
}
```

### IPC API

```typescript
// 窗口句柄
window.electronAPI.getWindowHandle(): Promise<{success: boolean, windowHandle: number}>

// MPV 进程管理
window.electronAPI.startMPV(videoId, videoPath, windowHandle, options): Promise<{success: boolean}>
window.electronAPI.stopMPV(videoId): Promise<{success: boolean}>

// 播放控制
window.electronAPI.mpvPlay(videoId): Promise<{success: boolean}>
window.electronAPI.mpvPause(videoId): Promise<{success: boolean}>
window.electronAPI.mpvSeek(videoId, time): Promise<{success: boolean}>
window.electronAPI.mpvSetVolume(videoId, volume): Promise<{success: boolean}>
window.electronAPI.mpvSetMute(videoId, muted): Promise<{success: boolean}>

// 状态查询
window.electronAPI.mpvGetCurrentTime(videoId): Promise<{success: boolean, currentTime: number}>
window.electronAPI.mpvGetDuration(videoId): Promise<{success: boolean, duration: number}>
window.electronAPI.mpvGetPlaybackState(videoId): Promise<{success: boolean, state: object}>
```

## 🔍 故障排除

### 常见问题

#### 1. MPV 进程启动失败

**症状**: 视频无法加载，显示初始化失败

**解决方案**:
```bash
# 检查 MPV 二进制文件
ls -la mpv/

# 重新下载 MPV
npm run setup:mpv

# 检查文件权限（Linux/macOS）
chmod +x mpv/*/mpv
```

#### 2. 视频无法显示

**症状**: 音频正常但视频黑屏

**解决方案**:
- 检查窗口句柄获取是否成功
- 验证 `--wid` 参数是否正确传递
- 查看 MPV 进程日志

#### 3. 多视频不同步

**症状**: 多个视频播放时间不一致

**解决方案**:
- 调整同步间隔: `syncIntervalMs`
- 增加时间容差: `timeTolerance`
- 检查网络延迟和系统性能

#### 4. 内存泄漏

**症状**: 长时间使用后内存持续增长

**解决方案**:
- 确保 MPV 进程正确清理
- 检查事件监听器是否正确移除
- 验证组件卸载时的清理逻辑

### 调试工具

```bash
# 查看 MPV 进程
ps aux | grep mpv

# 查看应用日志
npm run log:view

# 性能分析
npm run dev
# 然后打开 Chrome DevTools (F12) → Performance
```

## 📊 性能优化

### 内存优化

- **进程管理**: 及时清理不用的 MPV 进程
- **事件清理**: 组件卸载时移除所有事件监听器
- **缓存控制**: 合理控制视频缓存大小

### CPU 优化

- **硬件加速**: 启用 GPU 硬件解码
- **同步优化**: 调整同步间隔和容差
- **线程管理**: 合理分配 MPV 工作线程

### 启动优化

- **预加载**: 预先启动 MPV 进程
- **缓存**: 缓存窗口句柄和配置
- **延迟加载**: 按需加载视频文件

## 🧪 测试

### 运行测试

```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# E2E 测试
npm run test:e2e

# MPV 特定测试
npm test -- --grep "MPV"
```

### 测试覆盖

- ✅ MPV 进程管理
- ✅ 窗口嵌入功能
- ✅ 播放控制
- ✅ 多视频同步
- ✅ 错误处理
- ✅ 跨平台兼容性

详细测试指南请参考: [MPV_TESTING_GUIDE.md](./MPV_TESTING_GUIDE.md)

## 🔄 迁移指南

### 从 HTML5 Video 迁移

1. **组件替换**:
   ```typescript
   // 旧代码
   import MultiVideoPlayer from './components/MultiVideoPlayer';

   // 新代码
   import MultiMPVPlayer from './components/MultiMPVPlayer';
   ```

2. **API 适配**:
   - 播放控制方法保持不变
   - 新增异步错误处理
   - 时间更新回调格式一致

3. **配置更新**:
   - 添加 MPV 二进制文件
   - 更新打包配置
   - 配置 IPC 处理程序

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/mpv-enhancement`
3. 提交更改: `git commit -am 'Add MPV enhancement'`
4. 推送分支: `git push origin feature/mpv-enhancement`
5. 创建 Pull Request

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 配置
- 添加适当的错误处理
- 编写单元测试

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 完整的 MPV 播放器集成
- ✨ 跨平台窗口嵌入支持
- ✨ 多视频同步播放
- ✨ 精准进度控制
- ✨ 自动化打包配置

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](../LICENSE) 文件

## 🙏 致谢

- [MPV](https://mpv.io/) - 强大的媒体播放器
- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [React](https://reactjs.org/) - 用户界面库
- [Chakra UI](https://chakra-ui.com/) - 简洁的组件库

---

**🎉 恭喜！您已成功集成 MPV 播放器到 Electron 应用中！**

如有问题或建议，请创建 [Issue](../../issues) 或联系开发团队。