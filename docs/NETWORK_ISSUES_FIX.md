# 网络连接问题解决方案

## 🐛 问题描述

安装依赖时出现网络连接错误：
```
RequestError
    at ClientRequest.<anonymous>
AggregateError [ETIMEDOUT]
error /Users/<USER>/node_modules/electron: Command failed.
```

这通常是由于：
1. 网络连接不稳定
2. 防火墙或代理设置
3. Electron 下载服务器访问受限
4. DNS 解析问题

## 🔧 解决方案

### 方案 1: 使用国内镜像源（推荐）

#### A. 环境变量方式
```bash
# 设置 Electron 镜像源
export ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"
export ELECTRON_CUSTOM_DIR="{{ version }}"

# 设置 npm 镜像源
export npm_config_registry="https://registry.npmmirror.com"

# 然后重新安装
rm -rf node_modules yarn.lock
yarn install
```

#### B. 配置文件方式
已创建 `.yarnrc` 文件，包含：
```
electron_mirror "https://npmmirror.com/mirrors/electron/"
electron_custom_dir "{{ version }}"
registry "https://registry.npmmirror.com"
```

### 方案 2: 分步安装

```bash
# 1. 清理环境
rm -rf node_modules yarn.lock

# 2. 先单独安装 Electron
yarn add electron@37.2.0 --dev

# 3. 再安装其他依赖
yarn install
```

### 方案 3: 使用 npm 替代 yarn

```bash
# npm 通常有更好的网络重试机制
rm -rf node_modules package-lock.json yarn.lock

# 使用 npm 安装
npm install
```

### 方案 4: 增加超时和重试

```bash
# 增加网络超时时间
yarn install --network-timeout 600000

# 或者使用 npm 的重试机制
npm install --fetch-retry-mintimeout 20000 --fetch-retry-maxtimeout 120000
```

### 方案 5: 离线安装

如果网络问题持续存在：

```bash
# 跳过可选依赖
yarn install --ignore-optional

# 或者使用缓存
yarn install --prefer-offline
```

## 🔍 网络诊断

### 1. 运行网络诊断脚本

```bash
yarn diagnose:network
```

这会测试各种镜像源的连接状态。

### 2. 手动测试连接

```bash
# 测试 GitHub 连接
curl -I https://github.com/electron/electron/releases

# 测试镜像源连接
curl -I https://npmmirror.com/mirrors/electron/

# 测试 npm 源连接
curl -I https://registry.npmmirror.com
```

### 3. 检查 DNS 解析

```bash
# 检查 DNS 解析
nslookup github.com
nslookup npmmirror.com
nslookup registry.npmjs.org
```

## 🌐 代理和防火墙设置

### 1. 如果使用代理

```bash
# 设置代理
export https_proxy=http://proxy-server:port
export http_proxy=http://proxy-server:port

# 或者使用 npm 配置
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port
```

### 2. 如果使用公司网络

```bash
# 可能需要设置证书
npm config set strict-ssl false  # 仅在必要时使用

# 或者设置自定义 CA
npm config set ca ""
```

## 📋 完整的修复流程

### 步骤 1: 诊断网络

```bash
# 运行网络诊断
yarn diagnose:network

# 或手动测试
curl -I https://npmmirror.com/mirrors/electron/
```

### 步骤 2: 配置镜像源

```bash
# 方法 A: 环境变量
export ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"
export ELECTRON_CUSTOM_DIR="{{ version }}"

# 方法 B: 使用已创建的 .yarnrc 文件（推荐）
# 文件已存在，包含镜像源配置
```

### 步骤 3: 清理并重新安装

```bash
# 完全清理
rm -rf node_modules yarn.lock package-lock.json

# 重新安装
yarn install

# 如果 yarn 仍有问题，使用 npm
npm install
```

### 步骤 4: 验证安装

```bash
# 检查 Electron 是否正确安装
./node_modules/.bin/electron --version

# 应该显示: v37.2.0
```

## ⚠️ 常见问题

### 1. 仍然超时

```bash
# 尝试更长的超时时间
yarn install --network-timeout 1200000  # 20分钟

# 或者分批安装
yarn add react react-dom
yarn add @chakra-ui/react
yarn add electron --dev
```

### 2. 证书错误

```bash
# 临时禁用 SSL 验证（不推荐用于生产）
npm config set strict-ssl false
yarn install
npm config set strict-ssl true  # 安装后恢复
```

### 3. 权限问题

```bash
# 检查文件权限
ls -la node_modules/

# 如果有权限问题，清理后重试
sudo rm -rf node_modules
yarn install
```

## 🚀 推荐的最佳实践

### 1. 使用镜像源

始终配置国内镜像源以获得更好的下载速度：
```bash
# 全局配置
npm config set registry https://registry.npmmirror.com
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
```

### 2. 网络重试策略

```bash
# 在 package.json 中添加
{
  "scripts": {
    "install:retry": "npm install --fetch-retry-mintimeout 20000 --fetch-retry-maxtimeout 120000"
  }
}
```

### 3. 离线缓存

```bash
# 使用 yarn 的离线缓存
yarn install --prefer-offline
```

## 📞 如果问题仍然存在

如果按照上述步骤仍然无法解决，请提供：

1. **网络诊断结果**：
   ```bash
   yarn diagnose:network > network-diagnosis.log
   ```

2. **详细错误日志**：
   ```bash
   yarn install --verbose > install.log 2>&1
   ```

3. **系统信息**：
   ```bash
   node --version
   yarn --version
   npm --version
   ```

4. **网络环境信息**：
   - 是否使用代理
   - 是否在公司网络
   - 防火墙设置

## 🎉 成功标志

安装成功后，应该能看到：
- ✅ `node_modules/electron` 目录存在
- ✅ `./node_modules/.bin/electron --version` 显示正确版本
- ✅ 没有网络相关的错误信息
- ✅ 可以正常运行 `yarn build:windows-x64`
