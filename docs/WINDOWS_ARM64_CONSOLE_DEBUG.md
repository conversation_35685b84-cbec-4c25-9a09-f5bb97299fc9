# Windows ARM64 构建控制台调试

## 📋 概述

为了便于排查 Windows ARM64 构建的问题，通过环境变量和代码修改的方式启用了开发者工具和控制台调试。

## 🔧 修改内容

### 主进程代码修改

**文件**: `electron/main.js`

**修改位置**:
1. 添加了新的环境变量检查 `MEEA_WINDOWS_ARM64_DEBUG`
2. 在生产环境中启用开发者工具（当调试模式启用时）

```javascript
// 调试模式检测
const isDebugMode = forceDebugMode ||
                   isDebugBuild ||
                   process.env.DEBUG_MODE === 'true' ||
                   process.env.MEEA_DEBUG === 'true' ||
                   process.env.MEEA_WINDOWS_ARM64_DEBUG === 'true' || // ← 新增
                   process.argv.includes('--debug') ||
                   process.argv.includes('--verbose');

// 生产环境开发者工具启用
if (isDebugMode) {
  console.log('🐛 [DEBUG] 生产环境启用开发者工具 (调试模式)');
  mainWindow.webContents.openDevTools(); // ← 新增
}
```

### 构建脚本

**新增文件**:
- `scripts/build-windows-arm64-debug.sh` (Unix/Linux/macOS)
- `scripts/build-windows-arm64-debug.bat` (Windows)

## 🎯 效果

启用调试模式后，Windows ARM64 构建版本将会：

1. **自动打开开发者工具** - 应用启动时会自动打开 Chrome DevTools
2. **显示控制台输出** - 所有 `console.log`、`console.error` 等输出会显示在开发者工具的控制台中
3. **便于问题排查** - 可以实时查看应用运行时的日志、错误信息和网络请求
4. **支持断点调试** - 可以在开发者工具中设置断点进行调试

## 🚨 注意事项

### 临时性质
- ⚠️ **这是临时修改**，仅用于调试目的
- ⚠️ **正式发布前需要移除调试环境变量**
- ⚠️ **只影响设置了调试环境变量的构建**，其他构建不受影响

### 用户体验影响
- 用户会看到自动打开的开发者工具窗口
- 可能会影响应用的专业外观
- 开发者工具可以被用户关闭，不会影响应用正常运行

## 🔄 如何恢复

当调试完成后，有两种方式恢复正常构建：

### 方式1: 使用正常构建命令
直接使用正常的构建命令，不设置调试环境变量：

```bash
yarn build:windows-arm64
```

### 方式2: 移除代码中的调试逻辑（可选）
如果需要完全移除调试功能，可以编辑 `electron/main.js`：

```javascript
// 移除这一行：
process.env.MEEA_WINDOWS_ARM64_DEBUG === 'true' ||

// 或者注释掉：
// process.env.MEEA_WINDOWS_ARM64_DEBUG === 'true' ||
```

## 📝 构建命令

### 方式1: 使用 npm 脚本（推荐）

```bash
yarn build:windows-arm64-debug
```

### 方式2: 使用调试脚本

**Unix/Linux/macOS:**
```bash
./scripts/build-windows-arm64-debug.sh
```

**Windows:**
```cmd
scripts\build-windows-arm64-debug.bat
```

### 方式3: 手动设置环境变量

**Unix/Linux/macOS:**
```bash
export MEEA_WINDOWS_ARM64_DEBUG=true
export DEBUG_MODE=true
yarn build:windows-arm64
```

**Windows:**
```cmd
set MEEA_WINDOWS_ARM64_DEBUG=true
set DEBUG_MODE=true
yarn build:windows-arm64
```

### 构建产物
- 文件名：`MEEA-VIOFO-Setup-{version}-windows-arm64.exe`
- 位置：`dist/` 目录
- 特性：自动打开开发者工具，便于调试

## 🔍 调试技巧

### 查看启动日志
1. 运行构建的 ARM64 版本
2. 观察控制台窗口中的启动信息
3. 注意任何错误或警告信息

### 常见调试信息
- 应用初始化过程
- 文件路径解析
- FFmpeg 和 ExifTool 路径检测
- 视频处理过程
- 错误堆栈跟踪

### 日志文件
即使启用了控制台，应用仍会将日志写入文件：
- Windows: `%APPDATA%/MEEA-VIOFO/logs/`
- 可通过应用内的日志查看器访问

## ⏰ 记录

- **创建时间**: 2025-07-31
- **目的**: 临时调试 Windows ARM64 构建问题
- **影响范围**: 仅 `build:windows-arm64` 命令
- **状态**: 临时启用，需要后续移除
