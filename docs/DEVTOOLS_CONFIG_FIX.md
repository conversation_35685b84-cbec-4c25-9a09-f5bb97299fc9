# 开发者工具配置修复

## 🐛 问题描述

用户反馈在 Windows 调试版本中，快捷键（F12、Ctrl+Shift+I、Ctrl+Shift+J）无法打开开发者工具。

## 🔍 问题分析

经过检查发现，问题出在 BrowserWindow 的 `webPreferences.devTools` 配置上：

### 原始配置问题
```javascript
const isDev = process.env.NODE_ENV === 'development';

// 主窗口配置
webPreferences: {
  devTools: true  // 硬编码为 true
}

// 其他窗口配置
webPreferences: {
  devTools: isDev  // 在生产环境中为 false！
}
```

### 问题根源
1. **生产环境中 `isDev` 为 `false`** - 因为 `NODE_ENV !== 'development'`
2. **部分窗口的 `devTools` 被设置为 `false`** - 导致开发者工具无法启用
3. **快捷键注册成功但无法生效** - 因为底层的 devTools 被禁用

## ✅ 解决方案

### 1. 引入新的配置变量
```javascript
// 开发者工具启用条件：开发模式 OR 调试模式
const devToolsEnabled = isDev || isDebugMode;
```

### 2. 统一所有窗口的配置
```javascript
// 主窗口
webPreferences: {
  devTools: devToolsEnabled  // 统一使用新变量
}

// 日志查看器窗口
webPreferences: {
  devTools: devToolsEnabled  // 统一使用新变量
}

// 图片预览窗口
webPreferences: {
  devTools: devToolsEnabled  // 统一使用新变量
}
```

### 3. 添加配置调试日志
```javascript
console.log('🔧 [CONFIG] 应用配置信息:');
console.log(`  - isDev: ${isDev}`);
console.log(`  - isDebugMode: ${isDebugMode}`);
console.log(`  - devToolsEnabled: ${devToolsEnabled}`);
console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`  - isPackaged: ${isPackaged}`);
```

## 🔧 修复内容

### 修改的文件
- `electron/main.js` - 主要修复文件

### 具体修改
1. **新增配置变量**：
   ```javascript
   const devToolsEnabled = isDev || isDebugMode;
   ```

2. **更新主窗口配置**：
   ```javascript
   devTools: devToolsEnabled // 替换原来的 devTools: true
   ```

3. **更新其他窗口配置**：
   ```javascript
   devTools: devToolsEnabled // 替换原来的 devTools: isDev
   ```

4. **添加配置日志**：
   - 启动时输出详细的配置信息
   - 便于调试和问题排查

## 📊 预期效果

### 调试版本中的配置输出
```
🔧 [CONFIG] 应用配置信息:
  - isDev: false
  - isDebugMode: true
  - devToolsEnabled: true  ← 关键！必须为 true
  - NODE_ENV: production
  - isPackaged: true
```

### 快捷键功能
- **F12** - 切换开发者工具 ✅
- **Ctrl+Shift+I** - 切换开发者工具 ✅
- **Ctrl+Shift+J** - 打开开发者工具 ✅

## 🧪 测试方法

### 1. 构建调试版本
```bash
yarn build:windows-x64-debug
```

### 2. 检查配置日志
启动应用后，在开发者工具控制台中查找：
```
🔧 [CONFIG] 应用配置信息:
  - devToolsEnabled: true  ← 必须为 true
```

### 3. 测试快捷键
- 按 F12 - 应该能切换开发者工具
- 按 Ctrl+Shift+I - 应该能切换开发者工具
- 按 Ctrl+Shift+J - 应该能打开开发者工具

### 4. 检查快捷键日志
每次按快捷键都应该看到对应的日志：
```
🛠️ [DEBUG] F12 快捷键触发，切换开发者工具
🛠️ [DEBUG] Ctrl+Shift+I 快捷键触发，切换开发者工具
🛠️ [DEBUG] Ctrl+Shift+J 快捷键触发，打开开发者工具
```

## 🎯 关键要点

### 1. 配置优先级
```
devToolsEnabled = isDev || isDebugMode
```
- 开发模式：`isDev = true` → `devToolsEnabled = true`
- 调试模式：`isDebugMode = true` → `devToolsEnabled = true`
- 生产模式：`isDev = false, isDebugMode = false` → `devToolsEnabled = false`

### 2. 环境变量检查
调试模式通过以下环境变量启用：
- `MEEA_WINDOWS_X64_DEBUG=true`
- `MEEA_WINDOWS_ARM64_DEBUG=true`
- `MEEA_WINDOWS_DEBUG=true`
- `DEBUG_MODE=true`
- `MEEA_DEBUG=true`

### 3. 构建命令
- **调试版本**：`yarn build:windows-x64-debug`
- **正常版本**：`yarn build:windows-x64`

## ⚠️ 重要提醒

### 安全考虑
- 只有调试版本启用开发者工具
- 正常版本不会启用开发者工具
- 生产发布时必须使用正常构建命令

### 性能影响
- 开发者工具会消耗额外的系统资源
- 仅在需要调试时使用调试版本

## 📈 修复验证

### 修复前
- ❌ 快捷键无响应
- ❌ `devTools: isDev` 在生产环境为 `false`
- ❌ 开发者工具无法通过快捷键打开

### 修复后
- ✅ 快捷键正常响应
- ✅ `devTools: devToolsEnabled` 在调试模式为 `true`
- ✅ 开发者工具可以通过快捷键正常操作
- ✅ 配置日志清晰显示状态

## 🔄 回滚方案

如果需要回滚，可以：
1. 移除 `devToolsEnabled` 变量
2. 恢复原来的 `devTools: true` 和 `devTools: isDev` 配置
3. 移除配置日志输出

但建议保留此修复，因为它解决了一个重要的配置不一致问题。

---

**修复时间**: 2025-08-01  
**影响版本**: MEEA-VIOFO v25.07.18-1805+  
**修复状态**: 已完成，待测试验证
