# 路径和文件加载调试测试

## 🎯 测试目标

验证构建后的 Windows 应用中文件路径是否正确，以及开发者工具是否能在文件加载完成后正常打开。

## 🔍 关键差异分析

### 开发模式 vs 生产模式

| 项目 | 开发模式 (`yarn dev`) | 生产模式 (构建后) |
|------|---------------------|------------------|
| NODE_ENV | `development` | `production` |
| isDev | `true` | `false` |
| 加载方式 | `loadURL('http://localhost:5174')` | `loadFile('../dist/index.html')` |
| 开发者工具 | 立即打开 | 需要等待文件加载完成 |
| 文件路径 | 网络URL | 本地文件路径 |

## 📋 测试步骤

### 1. 安装并启动应用
- 使用最新构建的 `MEEA-VIOFO-Setup-25.7.18-1805-windows-x64.exe`
- 启动应用

### 2. 检查路径调试信息

在开发者工具控制台中查找以下路径信息：

```
🔧 [CONFIG] 应用配置信息:
  - isDev: false
  - isDebugMode: true
  - devToolsEnabled: true
  - NODE_ENV: production
  - isPackaged: true

🚀 [PROD] 加载生产模式文件...
🚀 [PROD] __dirname: C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\resources\app.asar\electron
🚀 [PROD] process.cwd(): C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO
🚀 [PROD] app.getAppPath(): C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\resources\app.asar
🚀 [PROD] 计算的文件路径: C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\resources\app.asar\dist\index.html
🚀 [PROD] 文件是否存在: true/false
```

**关键检查点**：
- `devToolsEnabled` 必须为 `true`
- `文件是否存在` 必须为 `true`
- 路径应该指向正确的 `index.html` 文件

### 3. 检查文件加载事件

查找文件加载相关的日志：

```
✅ [DEBUG] 页面加载完成，尝试打开开发者工具...
🛠️ [DEBUG] openDevTools() 调用成功
✅ [DEBUG] 开发者工具已成功打开
```

或者如果失败：

```
❌ [DEBUG] 页面加载失败: -6 ERR_FILE_NOT_FOUND
❌ [DEBUG] openDevTools() 调用失败: Error: ...
```

### 4. 验证开发者工具状态

检查开发者工具是否实际打开：
- 应该看到开发者工具窗口
- 控制台中应该有应用日志
- 可以查看 Elements、Network 等标签页

## 🔧 可能的问题和解决方案

### 问题1: 文件路径错误

**症状**：
```
🚀 [PROD] 文件是否存在: false
❌ [DEBUG] 页面加载失败: -6 ERR_FILE_NOT_FOUND
```

**可能原因**：
- 构建后的文件结构与预期不符
- `__dirname` 在打包后的路径不正确

**解决方案**：
- 检查 `dist/index.html` 是否存在
- 验证 electron-builder 的文件包含配置

### 问题2: 开发者工具无法打开

**症状**：
```
✅ [DEBUG] 页面加载完成，尝试打开开发者工具...
❌ [DEBUG] openDevTools() 调用失败: Error: DevTools was disconnected from the page.
```

**可能原因**：
- `devTools: false` 在某个地方被设置
- webContents 状态异常

**解决方案**：
- 确认所有 BrowserWindow 的 `devTools` 配置都是 `devToolsEnabled`
- 检查是否有其他代码禁用了开发者工具

### 问题3: 页面加载但开发者工具不响应

**症状**：
```
✅ [DEBUG] 页面加载完成，尝试打开开发者工具...
🛠️ [DEBUG] openDevTools() 调用成功
⚠️ [DEBUG] 开发者工具未打开，尝试分离模式...
```

**可能原因**：
- 开发者工具被某些安全策略阻止
- 窗口焦点或显示问题

**解决方案**：
- 尝试分离模式：`openDevTools({ mode: 'detach' })`
- 检查 Windows 安全设置

## 🧪 手动测试方法

### 1. 检查文件结构
在安装目录中验证文件是否存在：
```
C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\resources\app.asar
```

### 2. 使用 asar 工具检查
```bash
npx asar list "C:\Users\<USER>\AppData\Local\Programs\MEEA-VIOFO\resources\app.asar"
```

应该能看到 `dist/index.html` 在列表中。

### 3. 强制打开开发者工具
如果自动打开失败，尝试：
- 在调试对话框中点击"重新打开开发者工具"
- 使用菜单栏的"调试" -> "开发者工具"（如果存在）

## 📊 预期结果

### 成功的日志序列
```
🔧 [CONFIG] 应用配置信息:
  - devToolsEnabled: true

🚀 [PROD] 文件是否存在: true
✅ [DEBUG] 页面加载完成，尝试打开开发者工具...
🛠️ [DEBUG] openDevTools() 调用成功
✅ [DEBUG] 开发者工具已成功打开

🎉 调试模式已启用！
📋 应用信息:
  - 版本: 25.07.18-1805
  - 构建时间: 2025/8/1 16:22:44
  - 平台: win32 x64
  - 调试模式: true
```

### 失败的日志序列
```
🔧 [CONFIG] 应用配置信息:
  - devToolsEnabled: false  ← 问题！

或者：

🚀 [PROD] 文件是否存在: false  ← 问题！
❌ [DEBUG] 页面加载失败: -6 ERR_FILE_NOT_FOUND
```

## 🔄 下一步行动

根据测试结果：

1. **如果路径正确但开发者工具不打开**：
   - 检查 `devToolsEnabled` 配置
   - 验证 webContents 状态

2. **如果文件路径错误**：
   - 修复构建配置
   - 调整文件路径计算逻辑

3. **如果页面加载失败**：
   - 检查 electron-builder 配置
   - 验证文件包含设置

---

**创建时间**: 2025-08-01  
**目标**: 诊断开发者工具无法打开的根本原因  
**重点**: 文件路径和加载时序问题
