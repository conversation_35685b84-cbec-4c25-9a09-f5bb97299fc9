# Windows 调试构建指南

## 📋 概述

为了便于排查 Windows 平台的问题，我们提供了多种调试构建选项，支持 x64 和 ARM64 架构。

## 🚀 快速开始

### 推荐方式：使用 npm 脚本

```bash
# Windows x64 调试版本
yarn build:windows-x64-debug

# Windows ARM64 调试版本  
yarn build:windows-arm64-debug
```

## 🔧 所有构建方式

### 1. npm 脚本（推荐）

| 命令 | 架构 | 说明 |
|------|------|------|
| `yarn build:windows-x64-debug` | x64 | Windows x64 调试版本 |
| `yarn build:windows-arm64-debug` | ARM64 | Windows ARM64 调试版本 |

### 2. 构建脚本

**Unix/Linux/macOS:**
```bash
# x64 版本
./scripts/build-windows-x64-debug.sh

# ARM64 版本
./scripts/build-windows-arm64-debug.sh
```

**Windows:**
```cmd
REM x64 版本
scripts\build-windows-x64-debug.bat

REM ARM64 版本
scripts\build-windows-arm64-debug.bat
```

### 3. 手动设置环境变量

**Windows x64:**
```bash
# Unix/Linux/macOS
export MEEA_WINDOWS_X64_DEBUG=true
export DEBUG_MODE=true
yarn build:windows-x64

# Windows
set MEEA_WINDOWS_X64_DEBUG=true
set DEBUG_MODE=true
yarn build:windows-x64
```

**Windows ARM64:**
```bash
# Unix/Linux/macOS
export MEEA_WINDOWS_ARM64_DEBUG=true
export DEBUG_MODE=true
yarn build:windows-arm64

# Windows
set MEEA_WINDOWS_ARM64_DEBUG=true
set DEBUG_MODE=true
yarn build:windows-arm64
```

## 🎯 调试功能

### 自动启用的功能
1. **开发者工具** - 应用启动时自动打开 Chrome DevTools
2. **控制台窗口** - Windows 平台会尝试创建额外的控制台窗口
3. **详细日志** - 所有调试信息输出到控制台和日志文件
4. **断点调试** - 支持在开发者工具中设置断点

### 环境变量说明

| 环境变量 | 作用 | 适用架构 |
|----------|------|----------|
| `MEEA_WINDOWS_X64_DEBUG` | 启用 Windows x64 调试 | x64 |
| `MEEA_WINDOWS_ARM64_DEBUG` | 启用 Windows ARM64 调试 | ARM64 |
| `MEEA_WINDOWS_DEBUG` | 启用通用 Windows 调试 | x64 + ARM64 |
| `DEBUG_MODE` | 启用全局调试模式 | 所有平台 |

## 📦 构建产物

### 文件命名
- **x64**: `MEEA-VIOFO-Setup-{version}-windows-x64.exe`
- **ARM64**: `MEEA-VIOFO-Setup-{version}-windows-arm64.exe`

### 文件大小
- **x64**: ~614 MB
- **ARM64**: ~609 MB

### 特性
- ✅ 自动打开开发者工具
- ✅ Windows 控制台窗口（尝试创建）
- ✅ 详细的调试日志
- ✅ 支持断点调试
- ✅ 网络请求监控
- ✅ 性能分析工具

## 🔍 调试技巧

### 1. 查看启动日志
- 应用启动时观察开发者工具控制台
- 查找带有 `🐛 [DEBUG]` 标识的日志
- 注意任何错误或警告信息

### 2. Windows 控制台
- 应用会尝试创建额外的控制台窗口
- 控制台窗口标题：`MEEA-VIOFO Debug Console`
- 日志会同时输出到开发者工具和控制台窗口

### 3. 常见调试信息
- 应用初始化过程
- 文件路径解析
- FFmpeg 和 ExifTool 路径检测
- 视频处理过程
- GPS 数据提取
- 错误堆栈跟踪

### 4. 日志文件位置
- **Windows**: `%APPDATA%/MEEA-VIOFO/logs/`
- 可通过应用内的日志查看器访问

## 🔄 恢复正常构建

当调试完成后，使用正常构建命令：

```bash
# 正常 x64 构建
yarn build:windows-x64

# 正常 ARM64 构建
yarn build:windows-arm64
```

## ⚠️ 注意事项

### 临时性质
- ⚠️ **这是临时调试功能**，仅用于问题排查
- ⚠️ **正式发布前必须使用正常构建命令**
- ⚠️ **调试版本不适合最终用户使用**

### 用户体验影响
- 会自动打开开发者工具窗口
- 可能显示额外的控制台窗口
- 会消耗更多系统资源
- 可能暴露应用内部信息

### 安全考虑
- 开发者工具可能暴露敏感信息
- 不应在生产环境中使用
- 仅用于开发和调试目的

## 📝 测试建议

### 基本功能测试
1. 安装调试版本
2. 启动应用，观察是否自动打开开发者工具
3. 检查控制台是否有调试日志输出
4. 验证应用基本功能是否正常

### 问题排查流程
1. 重现问题
2. 查看开发者工具控制台
3. 检查网络请求
4. 分析错误堆栈
5. 记录相关日志信息

## 🎉 成功案例

- ✅ Windows x64 调试版本构建成功
- ✅ Windows ARM64 调试版本构建成功
- ✅ 开发者工具自动启用
- ✅ 控制台日志正常输出
- ✅ 所有构建脚本正常工作

---

**创建时间**: 2025-08-01  
**适用版本**: MEEA-VIOFO v25.07.18-1805+  
**状态**: 临时调试功能，需要后续移除
