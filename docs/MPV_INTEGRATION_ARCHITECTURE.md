# MPV 播放器集成架构设计

## 概述

本文档描述了将当前基于 HTML5 `<video>` 的播放器替换为 mpv 嵌入式播放器的完整架构设计。

## 当前架构分析

### 现有组件结构
1. **MultiVideoPlayer.tsx** - 主播放器组件
   - 管理多个 HTML5 video 元素
   - 处理视频同步播放、暂停、跳转
   - 支持缩放、全屏、多视频布局
   - 通过 videoRefs 管理 video DOM 元素

2. **VideoPlayerControls.tsx** - 播放控制组件
   - 播放/暂停按钮
   - 进度条拖拽控制
   - 音量控制
   - 时间显示

3. **App.tsx** - 主应用逻辑
   - 统一的播放状态管理 (isPlaying, masterTime, volume, etc.)
   - 播放控制函数 (handleVideoSeek, handleVideoVolumeChange, etc.)
   - 键盘快捷键处理

## MPV 集成架构设计

### 1. 进程管理架构

#### MPV 进程管理器 (electron/mpv-manager.js)
- 管理多个 MPV 进程实例
- 跨平台 MPV 可执行文件路径处理
- 进程生命周期管理（启动、监控、清理）
- 错误处理和自动重启机制

### 2. IPC 通信架构

#### MPV 控制接口 (electron/mpv-controller.js)
- JSON IPC 协议通信
- 命令队列和响应处理
- 播放控制方法封装
- 属性查询和监听

### 3. 窗口嵌入架构

#### 窗口句柄管理 (electron/window-handle.js)
- 跨平台窗口句柄获取
- MPV --wid 参数处理
- 嵌入容器创建和管理

### 4. 组件重构架构

#### 新组件结构
- **MPVPlayer.tsx** - 单个 MPV 播放器组件
- **MultiMPVPlayer.tsx** - 多 MPV 播放器组件（替换 MultiVideoPlayer）
- **VideoPlayerControls.tsx** - 更新的控制组件（保持现有 UI）

### 5. 多视频同步架构

#### 多 MPV 同步管理器
- 主从同步机制
- 定时器同步检查
- 时间差容错处理
- 播放状态统一管理

## 技术实现要点

### 跨平台兼容性
- Windows: mpv.exe + 窗口句柄
- macOS: mpv + Cocoa 窗口集成
- Linux: mpv + X11/Wayland 窗口集成

### 进度条精准控制
- 实时时间位置查询
- 拖拽事件处理
- 平滑跳转实现

### 错误处理机制
- MPV 进程监控
- 连接失败重试
- 降级到 HTML5 video

## 实施计划

1. 创建 MPV 进程管理模块
2. 实现 MPV 控制接口
3. 实现窗口嵌入功能
4. 重构播放器组件
5. 更新控制逻辑
6. 配置二进制打包
7. 实现进度条精准控制
8. 集成测试和优化

## 文件结构

```
electron/
├── mpv-manager.js          # MPV 进程管理
├── mpv-controller.js       # MPV 控制接口
├── window-handle.js        # 窗口句柄管理
└── multi-mpv-sync.js       # 多视频同步

src/components/
├── MPVPlayer.tsx           # 单个 MPV 播放器组件
├── MultiMPVPlayer.tsx      # 多 MPV 播放器组件
└── VideoPlayerControls.tsx # 更新的控制组件

mpv/                        # MPV 二进制文件
├── win-x64/mpv.exe
├── win-arm64/mpv.exe
├── mac-x64/mpv
├── mac-arm64/mpv
├── linux-x64/mpv
└── linux-arm64/mpv
```