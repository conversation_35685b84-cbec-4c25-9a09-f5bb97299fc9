# 强制开发者工具版本

## 🎯 修改说明

这个版本**强制启用开发者工具**，绕过所有条件检查，确保开发者工具能够正常打开。

## 🔧 关键修改

### 1. **强制启用 devTools 配置**
```javascript
// 原来：devTools: devToolsEnabled
// 现在：devTools: true  // 强制启用开发者工具
```

### 2. **强制打开开发者工具**
```javascript
// 强制打开开发者工具（无条件）
console.log('🛠️ [FORCE] 强制打开开发者工具...');

// 立即尝试打开
setTimeout(() => {
  mainWindow.webContents.openDevTools();
  console.log('🛠️ [FORCE] 立即打开开发者工具 - 成功');
}, 100);
```

### 3. **强制注册快捷键**
```javascript
// 强制注册开发者工具快捷键（无条件）
// 不再检查 isDebugMode 条件
const f12Success = globalShortcut.register('F12', () => {
  mainWindow.webContents.toggleDevTools();
  console.log('🛠️ [FORCE] F12 快捷键触发，切换开发者工具');
});
```

### 4. **强制启用窗口事件监听**
```javascript
// 强制启用窗口级别的键盘事件监听
mainWindow.webContents.on('before-input-event', (event, input) => {
  if (input.key === 'F12' && input.type === 'keyDown') {
    mainWindow.webContents.toggleDevTools();
    console.log('🛠️ [FORCE] F12 窗口事件触发，切换开发者工具');
  }
  // ... 其他快捷键
});
```

## 📦 版本信息

- **文件**: `MEEA-VIOFO-Setup-25.7.18-1805-windows-x64.exe` (614 MB)
- **构建时间**: 2025-08-01 17:20
- **特点**: 强制启用开发者工具，无条件检查

## 🧪 预期效果

安装这个版本后，应该会看到：

### 1. **自动打开开发者工具**
- 应用启动后100毫秒内尝试打开开发者工具
- 不依赖任何配置条件

### 2. **日志输出**
```
🛠️ [FORCE] 强制打开开发者工具...
🛠️ [FORCE] 立即打开开发者工具 - 成功
🔧 [FORCE] 强制注册快捷键...
🔧 [FORCE] 全局快捷键注册结果:
  - F12: ✅
  - Ctrl+Shift+I: ✅
  - Ctrl+Shift+J: ✅
🔧 [FORCE] 窗口级别键盘事件监听已启用
```

### 3. **快捷键响应**
按下快捷键时应该看到：
```
🛠️ [FORCE] F12 快捷键触发，切换开发者工具
🛠️ [FORCE] Ctrl+Shift+I 快捷键触发，切换开发者工具
🛠️ [FORCE] Ctrl+Shift+J 快捷键触发，打开开发者工具
```

## 🔍 测试重点

### 1. **开发者工具是否自动打开**
- 应用启动后立即查看是否有开发者工具窗口
- 如果没有，检查日志中的错误信息

### 2. **快捷键是否工作**
- 测试 F12、Ctrl+Shift+I、Ctrl+Shift+J
- 每次按键都应该有对应的日志输出

### 3. **日志文件内容**
查找以下关键日志：
- `[FORCE] 强制打开开发者工具...`
- `[FORCE] 全局快捷键注册结果:`
- `[FORCE] 窗口级别键盘事件监听已启用`

## 🚨 重要说明

### 这是诊断版本
- **仅用于调试目的**
- **不适合最终用户使用**
- **会强制打开开发者工具**

### 如果仍然无法打开
如果这个强制版本仍然无法打开开发者工具，可能的原因：

1. **Electron 版本问题** - 某些 Electron 版本可能有 bug
2. **Windows 系统限制** - 安全软件或系统策略阻止
3. **应用签名问题** - 代码签名可能影响开发者工具
4. **硬件加速问题** - GPU 相关问题

### 下一步诊断
如果强制版本仍然失败，我们需要：
1. 检查 Electron 版本兼容性
2. 尝试禁用硬件加速
3. 检查 Windows 事件查看器
4. 尝试不同的开发者工具打开模式

## 📋 测试清单

- [ ] 应用能正常启动
- [ ] 开发者工具自动打开
- [ ] F12 快捷键工作
- [ ] Ctrl+Shift+I 快捷键工作  
- [ ] Ctrl+Shift+J 快捷键工作
- [ ] 日志中有 `[FORCE]` 相关信息
- [ ] 快捷键触发时有对应日志

---

**创建时间**: 2025-08-01  
**版本类型**: 强制开发者工具版本  
**目的**: 绕过所有条件检查，强制启用开发者工具
