# MPV 播放器集成测试指南

## 概述

本文档提供了 MPV 播放器集成的完整测试指南，包括功能测试、性能测试和跨平台兼容性测试。

## 测试前准备

### 1. 安装 MPV 二进制文件

```bash
# 运行 MPV 设置脚本
npm run setup:mpv

# 或者手动下载 MPV 二进制文件到对应目录：
# mpv/win-x64/mpv.exe
# mpv/win-arm64/mpv.exe
# mpv/mac-x64/mpv
# mpv/mac-arm64/mpv
# mpv/linux-x64/mpv
# mpv/linux-arm64/mpv
```

### 2. 准备测试视频文件

- 准备不同格式的视频文件（MP4, AVI, MOV, MKV）
- 准备不同分辨率的视频（720p, 1080p, 4K）
- 准备不同时长的视频（短视频 < 1分钟，长视频 > 10分钟）

### 3. 启动开发环境

```bash
npm run dev
```

## 功能测试清单

### 基础播放功能

- [ ] **视频加载**
  - [ ] 单个视频文件加载
  - [ ] 多个视频文件同时加载
  - [ ] 不同格式视频文件支持
  - [ ] 大文件加载性能

- [ ] **播放控制**
  - [ ] 播放/暂停切换
  - [ ] 播放状态同步（多视频）
  - [ ] 播放速度控制
  - [ ] 循环播放

- [ ] **进度控制**
  - [ ] 进度条拖拽跳转
  - [ ] 精确到秒的时间跳转
  - [ ] 键盘快捷键跳转（左右箭头）
  - [ ] 进度条实时更新
  - [ ] 悬停时间预览

- [ ] **音频控制**
  - [ ] 音量调节（0-100%）
  - [ ] 静音/取消静音
  - [ ] 音量同步（多视频）

### 多视频同步功能

- [ ] **同步播放**
  - [ ] 多视频同时播放
  - [ ] 播放状态同步
  - [ ] 时间同步精度（< 100ms）
  - [ ] 主从视频切换

- [ ] **同步控制**
  - [ ] 统一进度控制
  - [ ] 统一音量控制
  - [ ] 统一播放/暂停
  - [ ] 同步跳转

### 窗口嵌入功能

- [ ] **视频渲染**
  - [ ] 视频正确嵌入到 Electron 窗口
  - [ ] 视频比例保持正确
  - [ ] 窗口大小调整适应
  - [ ] 全屏模式支持

- [ ] **界面集成**
  - [ ] 控制界面覆盖正确
  - [ ] 鼠标事件响应正常
  - [ ] 键盘快捷键正常
  - [ ] 右键菜单禁用

### 错误处理

- [ ] **文件错误**
  - [ ] 不存在的文件处理
  - [ ] 损坏的视频文件处理
  - [ ] 不支持的格式处理
  - [ ] 权限不足的文件处理

- [ ] **进程错误**
  - [ ] MPV 进程启动失败处理
  - [ ] MPV 进程意外退出处理
  - [ ] IPC 连接失败处理
  - [ ] 命令超时处理

## 性能测试

### 内存使用

- [ ] **单视频播放**
  - [ ] 初始内存占用 < 200MB
  - [ ] 播放过程中内存稳定
  - [ ] 长时间播放无内存泄漏

- [ ] **多视频播放**
  - [ ] 4个视频同时播放内存 < 800MB
  - [ ] 内存使用随视频数量线性增长
  - [ ] 视频切换时内存正确释放

### CPU 使用

- [ ] **播放性能**
  - [ ] 1080p 视频播放 CPU < 30%
  - [ ] 4K 视频播放 CPU < 60%
  - [ ] 多视频播放 CPU 合理分配

- [ ] **同步性能**
  - [ ] 多视频同步 CPU 开销 < 5%
  - [ ] 同步精度保持在 100ms 内
  - [ ] 长时间同步无性能下降

### 启动性能

- [ ] **冷启动**
  - [ ] 应用启动时间 < 5秒
  - [ ] 首个视频加载时间 < 3秒
  - [ ] MPV 进程启动时间 < 2秒

- [ ] **热启动**
  - [ ] 视频切换时间 < 1秒
  - [ ] 多视频加载时间 < 5秒
  - [ ] 播放状态切换 < 500ms

## 跨平台兼容性测试

### Windows 平台

- [ ] **Windows 10 x64**
  - [ ] MPV 进程正常启动
  - [ ] 窗口嵌入正常工作
  - [ ] 所有播放功能正常
  - [ ] 性能指标达标

- [ ] **Windows 11 x64**
  - [ ] 高 DPI 显示支持
  - [ ] 多显示器支持
  - [ ] Windows 11 特性兼容

- [ ] **Windows ARM64**
  - [ ] ARM64 版本 MPV 正常工作
  - [ ] 性能表现可接受
  - [ ] 功能完整性

### macOS 平台

- [ ] **macOS Intel (x64)**
  - [ ] MPV 进程正常启动
  - [ ] Cocoa 窗口集成正常
  - [ ] 所有播放功能正常
  - [ ] 性能指标达标

- [ ] **macOS Apple Silicon (ARM64)**
  - [ ] 原生 ARM64 性能
  - [ ] Rosetta 2 兼容性（如需要）
  - [ ] Metal 硬件加速

### Linux 平台

- [ ] **Ubuntu x64**
  - [ ] X11 窗口系统支持
  - [ ] Wayland 支持（如适用）
  - [ ] 所有播放功能正常

- [ ] **其他发行版**
  - [ ] CentOS/RHEL 兼容性
  - [ ] Debian 兼容性
  - [ ] Arch Linux 兼容性

## 用户体验测试

### 界面响应

- [ ] **控制响应**
  - [ ] 按钮点击响应 < 100ms
  - [ ] 进度条拖拽流畅
  - [ ] 音量调节实时反馈
  - [ ] 快捷键响应及时

- [ ] **视觉反馈**
  - [ ] 加载状态清晰显示
  - [ ] 错误信息友好提示
  - [ ] 播放状态准确显示
  - [ ] 时间显示格式正确

### 稳定性

- [ ] **长时间使用**
  - [ ] 连续播放 2 小时无崩溃
  - [ ] 频繁操作无异常
  - [ ] 内存使用稳定
  - [ ] 性能无明显下降

- [ ] **异常恢复**
  - [ ] 网络中断恢复
  - [ ] 文件被删除处理
  - [ ] 系统休眠恢复
  - [ ] 外部显示器切换

## 自动化测试

### 单元测试

```bash
# 运行 MPV 相关单元测试
npm test -- --grep "MPV"
```

### 集成测试

```bash
# 运行完整集成测试
npm run test:integration
```

### 端到端测试

```bash
# 运行 E2E 测试
npm run test:e2e
```

## 问题排查

### 常见问题

1. **MPV 进程启动失败**
   - 检查 MPV 二进制文件是否存在
   - 检查文件权限
   - 查看错误日志

2. **视频无法显示**
   - 检查窗口句柄获取
   - 验证 --wid 参数
   - 检查视频格式支持

3. **音频无声音**
   - 检查系统音频设置
   - 验证 MPV 音频输出
   - 检查音量设置

4. **同步不准确**
   - 检查同步间隔设置
   - 验证时间容差配置
   - 查看同步日志

### 调试工具

- **开发者工具**: F12 打开 Chrome DevTools
- **日志查看**: 应用内日志查看器
- **进程监控**: 任务管理器/活动监视器
- **性能分析**: Chrome DevTools Performance 面板

## 测试报告模板

```markdown
# MPV 集成测试报告

## 测试环境
- 操作系统:
- 架构:
- Node.js 版本:
- Electron 版本:
- MPV 版本:

## 测试结果
- 功能测试: ✅/❌
- 性能测试: ✅/❌
- 兼容性测试: ✅/❌
- 用户体验测试: ✅/❌

## 发现的问题
1. 问题描述
   - 重现步骤
   - 预期结果
   - 实际结果
   - 严重程度

## 建议改进
1. 改进建议
   - 具体描述
   - 优先级
   - 预期效果
```

## 持续集成

### GitHub Actions

```yaml
name: MPV Integration Test
on: [push, pull_request]
jobs:
  test:
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Setup MPV
        run: npm run setup:mpv
      - name: Run tests
        run: npm test
```

这个测试指南确保了 MPV 集成的质量和稳定性，涵盖了从基础功能到高级特性的全面测试。