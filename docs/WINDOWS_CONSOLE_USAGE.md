# Windows 控制台调试使用指南

## 🚀 快速开始

### 构建调试版本

```bash
# Windows x64 调试版本（推荐）
yarn build:windows-x64-debug

# Windows ARM64 调试版本
yarn build:windows-arm64-debug
```

## 🎯 调试功能说明

当你运行调试版本的应用时，会自动启用以下调试功能：

### 1. 开发者工具 ✅
- **自动打开**: 应用启动时自动打开 Chrome DevTools
- **完整功能**: 控制台、网络、性能、调试器等所有功能
- **实时日志**: 所有 `console.log` 输出都会显示在控制台中

### 2. Windows 控制台窗口 ✅
应用会尝试创建多种类型的控制台窗口：

#### 方法1: 命令提示符窗口
- **窗口标题**: `MEEA-VIOFO Debug Console`
- **功能**: 实时显示应用日志
- **颜色**: 绿色文本，黑色背景
- **特性**: 自动滚动，实时更新

#### 方法2: HTML 日志查看器
- **自动打开**: 在默认浏览器中打开
- **实时刷新**: 每2秒自动刷新日志
- **彩色显示**: 不同级别的日志用不同颜色显示
- **易于阅读**: 类似控制台的字体和样式

### 3. 日志文件 ✅
- **位置**: Windows 临时目录 (`%TEMP%\meea-viofo-debug.log`)
- **格式**: 带时间戳的结构化日志
- **持久化**: 即使关闭控制台窗口，日志仍会保存

## 📋 使用步骤

### 步骤1: 构建调试版本
```bash
yarn build:windows-x64-debug
```

### 步骤2: 安装应用
1. 找到构建产物：`dist/MEEA-VIOFO-Setup-25.7.18-1805-windows-x64.exe`
2. 在 Windows 设备上运行安装程序
3. 安装完成后启动应用

### 步骤3: 观察调试输出
应用启动后，你会看到：

1. **主应用窗口** - 正常的应用界面
2. **开发者工具窗口** - 自动打开的 Chrome DevTools
3. **控制台窗口** - 黑色背景的命令提示符窗口
4. **HTML 日志查看器** - 在浏览器中打开的日志页面

### 步骤4: 查看日志信息
在开发者工具的控制台中，你会看到类似这样的日志：

```
🐛 [DEBUG] 生产环境启用开发者工具 (调试模式)
🪟 [DEBUG] 正在为 Windows 创建控制台窗口...
✅ Windows 控制台创建成功
🎉 Windows 控制台已创建！
📋 应用信息:
  - 版本: 25.07.18-1805
  - 构建时间: 2025/8/1 00:46:44
  - 平台: win32 x64
  - 调试模式: true
  - 日志文件: C:\Users\<USER>\AppData\Local\Temp\meea-viofo-debug.log
  - HTML 查看器: C:\Users\<USER>\AppData\Local\Temp\meea-viofo-console.html

🔍 开始监控应用日志...

=== 应用启动完成，开始记录运行日志 ===
```

## 🔍 调试技巧

### 查看启动过程
- 观察应用初始化日志
- 检查文件路径解析
- 验证 FFmpeg 和 ExifTool 路径

### 监控视频处理
- 查看视频文件加载日志
- 监控 FFmpeg 命令执行
- 观察 GPS 数据提取过程

### 错误排查
- 查看错误堆栈跟踪
- 检查网络请求失败
- 分析文件操作错误

### 性能分析
- 使用开发者工具的性能面板
- 监控内存使用情况
- 分析渲染性能

## 📁 日志文件位置

### 临时日志文件
- **路径**: `%TEMP%\meea-viofo-debug.log`
- **内容**: 实时应用日志
- **格式**: `[时间] [级别] 消息内容`

### 应用日志文件
- **路径**: `%APPDATA%\MEEA-VIOFO\logs\`
- **内容**: 应用运行日志
- **访问**: 通过应用内的日志查看器

## ⚠️ 注意事项

### 性能影响
- 调试版本会消耗更多系统资源
- 多个控制台窗口会占用内存
- 日志写入会影响磁盘性能

### 安全考虑
- 开发者工具可能暴露敏感信息
- 日志文件可能包含用户数据
- 仅在受控环境中使用

### 用户体验
- 会显示多个额外窗口
- 可能让用户感到困惑
- 不适合最终用户使用

## 🔄 恢复正常版本

当调试完成后，构建正常版本：

```bash
# 正常 x64 版本
yarn build:windows-x64

# 正常 ARM64 版本
yarn build:windows-arm64
```

## 🆘 故障排除

### 控制台窗口没有出现
1. 检查 Windows 安全设置
2. 确认应用有足够权限
3. 查看开发者工具中的错误信息
4. 手动打开日志文件查看内容

### 日志没有更新
1. 检查日志文件权限
2. 确认临时目录可写
3. 重启应用重新创建日志文件

### HTML 查看器无法打开
1. 检查默认浏览器设置
2. 手动打开 HTML 文件
3. 使用文本编辑器查看日志文件

## 📞 技术支持

如果遇到问题，请提供以下信息：
- Windows 版本和架构
- 应用版本和构建时间
- 开发者工具中的错误信息
- 日志文件内容（如果可访问）

---

**创建时间**: 2025-08-01  
**适用版本**: MEEA-VIOFO v25.07.18-1805+  
**状态**: 临时调试功能
