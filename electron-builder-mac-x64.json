{"appId": "com.meea.viofo", "productName": "MEEA-VIOFO", "copyright": "Copyright © 2024 MEEA", "artifactName": "${productName}-${version}-${os}-${arch}.${ext}", "directories": {"output": "dist", "buildResources": "build"}, "files": ["dist/assets/**/*", "dist/index.html", "dist/*.svg", "dist/*.png", "electron/**/*", "keys/**/*", "package.json", "node_modules/fluent-ffmpeg/**/*", "node_modules/exiftool-vendored/**/*", "!node_modules/exiftool-vendored/bin/**/*", "!ffmpeg/**/*", "!dist/mac/**/*", "!dist/mac-arm64/**/*", "!dist/linux/**/*", "!dist/win-*/**/*", "!dist/*.dmg", "!dist/*.zip", "!dist/*.exe", "!dist/*.AppImage", "!dist/*.tar.gz", "!dist/*.deb", "!dist/*.rpm", "!dist/builder-*.yml", "!dist/builder-*.yaml", "!node_modules/**/prebuilds/win32-*/**/*", "!node_modules/**/prebuilds/linux-*/**/*", "!node_modules/**/prebuilds/darwin-arm64/**/*", "!node_modules/**/bin/win32-*/**/*", "!node_modules/**/bin/linux-*/**/*", "!node_modules/**/bin/darwin-arm64/**/*"], "extraResources": [], "mac": {"icon": "build/icons/icon.icns", "category": "public.app-category.video", "hardenedRuntime": false, "gatekeeperAssess": false, "identity": null, "type": "distribution", "minimumSystemVersion": "10.15.0", "files": ["!ffmpeg/win-*/**/*", "!ffmpeg/linux-*/**/*", "!ffmpeg/mac-arm64/**/*"], "extraResources": [{"from": "ffmpeg/mac-x64", "to": "ffmpeg/mac-x64", "filter": ["**/*"]}], "target": [{"target": "dmg", "arch": ["x64"]}, {"target": "zip", "arch": ["x64"]}], "extendInfo": {"NSCameraUsageDescription": "此应用需要访问摄像头以处理视频文件", "NSMicrophoneUsageDescription": "此应用需要访问麦克风以处理音频文件", "NSLocationUsageDescription": "此应用需要访问位置信息以显示GPS轨迹"}, "bundleVersion": "25.07.18-1805"}, "dmg": {"sign": false, "writeUpdateInfo": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}}