const logger = require('./logger');

/**
 * 多 MPV 同步管理器
 * 负责同步多个 MPV 播放器的播放状态
 */
class MultiMPVSyncManager {
  constructor() {
    this.controllers = new Map(); // videoId -> MPVController
    this.masterVideoId = null;
    this.syncInterval = null;
    this.syncIntervalMs = 100; // 同步间隔（毫秒）
    this.timeTolerance = 0.1; // 时间容差（秒）
    this.isPlaying = false;
    this.lastSyncTime = 0;
    this.syncStats = {
      totalSyncs: 0,
      syncErrors: 0,
      lastSyncDuration: 0
    };
  }

  /**
   * 添加视频控制器
   */
  addController(videoId, controller) {
    if (this.controllers.has(videoId)) {
      logger.warn('MultiMPVSync', `控制器已存在: ${videoId}`);
      return;
    }

    this.controllers.set(videoId, controller);

    // 如果是第一个控制器，设为主控制器
    if (!this.masterVideoId) {
      this.masterVideoId = videoId;
      logger.info('MultiMPVSync', `设置主控制器: ${videoId}`);
    }

    logger.info('MultiMPVSync', `添加控制器: ${videoId}, 总数: ${this.controllers.size}`);
  }

  /**
   * 移除视频控制器
   */
  removeController(videoId) {
    if (!this.controllers.has(videoId)) {
      logger.warn('MultiMPVSync', `控制器不存在: ${videoId}`);
      return;
    }

    this.controllers.delete(videoId);

    // 如果移除的是主控制器，选择新的主控制器
    if (this.masterVideoId === videoId) {
      const remainingIds = Array.from(this.controllers.keys());
      this.masterVideoId = remainingIds.length > 0 ? remainingIds[0] : null;

      if (this.masterVideoId) {
        logger.info('MultiMPVSync', `切换主控制器: ${this.masterVideoId}`);
      } else {
        logger.info('MultiMPVSync', '没有可用的主控制器');
        this.stopSyncTimer();
      }
    }

    logger.info('MultiMPVSync', `移除控制器: ${videoId}, 剩余: ${this.controllers.size}`);
  }

  /**
   * 设置主控制器
   */
  setMasterController(videoId) {
    if (!this.controllers.has(videoId)) {
      throw new Error(`控制器不存在: ${videoId}`);
    }

    this.masterVideoId = videoId;
    logger.info('MultiMPVSync', `手动设置主控制器: ${videoId}`);
  }

  /**
   * 同步播放
   */
  async syncPlay() {
    if (this.controllers.size === 0) {
      logger.warn('MultiMPVSync', '没有可用的控制器');
      return false;
    }

    try {
      logger.info('MultiMPVSync', `同步播放 ${this.controllers.size} 个视频`);

      const promises = Array.from(this.controllers.values()).map(async (controller) => {
        try {
          await controller.play();
        } catch (error) {
          logger.error('MultiMPVSync', `播放失败: ${error.message}`);
        }
      });

      await Promise.all(promises);
      this.isPlaying = true;
      this.startSyncTimer();

      return true;

    } catch (error) {
      logger.error('MultiMPVSync', `同步播放失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 同步暂停
   */
  async syncPause() {
    if (this.controllers.size === 0) {
      logger.warn('MultiMPVSync', '没有可用的控制器');
      return false;
    }

    try {
      logger.info('MultiMPVSync', `同步暂停 ${this.controllers.size} 个视频`);

      const promises = Array.from(this.controllers.values()).map(async (controller) => {
        try {
          await controller.pause();
        } catch (error) {
          logger.error('MultiMPVSync', `暂停失败: ${error.message}`);
        }
      });

      await Promise.all(promises);
      this.isPlaying = false;
      this.stopSyncTimer();

      return true;

    } catch (error) {
      logger.error('MultiMPVSync', `同步暂停失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 同步跳转
   */
  async syncSeek(time) {
    if (this.controllers.size === 0) {
      logger.warn('MultiMPVSync', '没有可用的控制器');
      return false;
    }

    try {
      logger.info('MultiMPVSync', `同步跳转到 ${time} 秒`);

      const promises = Array.from(this.controllers.values()).map(async (controller) => {
        try {
          await controller.seek(time);
        } catch (error) {
          logger.error('MultiMPVSync', `跳转失败: ${error.message}`);
        }
      });

      await Promise.all(promises);
      return true;

    } catch (error) {
      logger.error('MultiMPVSync', `同步跳转失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 同步音量
   */
  async syncVolume(volume) {
    if (this.controllers.size === 0) {
      return false;
    }

    try {
      const promises = Array.from(this.controllers.values()).map(async (controller) => {
        try {
          await controller.setVolume(volume);
        } catch (error) {
          logger.error('MultiMPVSync', `设置音量失败: ${error.message}`);
        }
      });

      await Promise.all(promises);
      return true;

    } catch (error) {
      logger.error('MultiMPVSync', `同步音量失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 同步静音状态
   */
  async syncMute(muted) {
    if (this.controllers.size === 0) {
      return false;
    }

    try {
      const promises = Array.from(this.controllers.values()).map(async (controller) => {
        try {
          await controller.setMute(muted);
        } catch (error) {
          logger.error('MultiMPVSync', `设置静音失败: ${error.message}`);
        }
      });

      await Promise.all(promises);
      return true;

    } catch (error) {
      logger.error('MultiMPVSync', `同步静音失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 启动同步定时器
   */
  startSyncTimer() {
    if (this.syncInterval || !this.masterVideoId || this.controllers.size <= 1) {
      return;
    }

    logger.info('MultiMPVSync', `启动同步定时器，间隔: ${this.syncIntervalMs}ms`);

    this.syncInterval = setInterval(async () => {
      await this.performSync();
    }, this.syncIntervalMs);
  }

  /**
   * 停止同步定时器
   */
  stopSyncTimer() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      logger.info('MultiMPVSync', '停止同步定时器');
    }
  }

  /**
   * 执行同步操作
   */
  async performSync() {
    if (!this.masterVideoId || !this.isPlaying) {
      return;
    }

    const startTime = Date.now();

    try {
      const masterController = this.controllers.get(this.masterVideoId);
      if (!masterController || !masterController.isConnected()) {
        logger.warn('MultiMPVSync', '主控制器不可用，跳过同步');
        return;
      }

      // 获取主视频的当前时间
      const masterTime = await masterController.getCurrentTime();
      if (masterTime === null || masterTime === undefined) {
        return;
      }

      // 同步其他视频到主视频时间
      const syncPromises = Array.from(this.controllers.entries())
        .filter(([id, controller]) =>
          id !== this.masterVideoId && controller.isConnected()
        )
        .map(async ([id, controller]) => {
          try {
            const currentTime = await controller.getCurrentTime();
            if (currentTime !== null && currentTime !== undefined) {
              const timeDiff = Math.abs(currentTime - masterTime);

              if (timeDiff > this.timeTolerance) {
                await controller.seek(masterTime);
                logger.debug('MultiMPVSync',
                  `同步视频 ${id}: ${currentTime.toFixed(2)}s -> ${masterTime.toFixed(2)}s (差异: ${timeDiff.toFixed(2)}s)`
                );
              }
            }
          } catch (error) {
            logger.error('MultiMPVSync', `同步视频 ${id} 失败: ${error.message}`);
            this.syncStats.syncErrors++;
          }
        });

      await Promise.all(syncPromises);

      this.syncStats.totalSyncs++;
      this.syncStats.lastSyncDuration = Date.now() - startTime;
      this.lastSyncTime = Date.now();

    } catch (error) {
      logger.error('MultiMPVSync', `执行同步失败: ${error.message}`);
      this.syncStats.syncErrors++;
    }
  }

  /**
   * 获取主视频的播放状态
   */
  async getMasterPlaybackState() {
    if (!this.masterVideoId) {
      return null;
    }

    const masterController = this.controllers.get(this.masterVideoId);
    if (!masterController || !masterController.isConnected()) {
      return null;
    }

    try {
      const [currentTime, duration, isPaused] = await Promise.all([
        masterController.getCurrentTime().catch(() => 0),
        masterController.getDuration().catch(() => 0),
        masterController.isPaused().catch(() => true)
      ]);

      return {
        currentTime: currentTime || 0,
        duration: duration || 0,
        isPaused: isPaused !== false,
        isPlaying: !isPaused
      };

    } catch (error) {
      logger.error('MultiMPVSync', `获取主视频状态失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取所有视频的播放状态
   */
  async getAllPlaybackStates() {
    const states = {};

    const promises = Array.from(this.controllers.entries()).map(async ([videoId, controller]) => {
      if (!controller.isConnected()) {
        states[videoId] = null;
        return;
      }

      try {
        const [currentTime, duration, isPaused] = await Promise.all([
          controller.getCurrentTime().catch(() => 0),
          controller.getDuration().catch(() => 0),
          controller.isPaused().catch(() => true)
        ]);

        states[videoId] = {
          currentTime: currentTime || 0,
          duration: duration || 0,
          isPaused: isPaused !== false,
          isPlaying: !isPaused,
          isMaster: videoId === this.masterVideoId
        };

      } catch (error) {
        logger.error('MultiMPVSync', `获取视频 ${videoId} 状态失败: ${error.message}`);
        states[videoId] = null;
      }
    });

    await Promise.all(promises);
    return states;
  }

  /**
   * 清理资源
   */
  cleanup() {
    logger.info('MultiMPVSync', '清理同步管理器');

    this.stopSyncTimer();
    this.controllers.clear();
    this.masterVideoId = null;
    this.isPlaying = false;

    // 重置统计信息
    this.syncStats = {
      totalSyncs: 0,
      syncErrors: 0,
      lastSyncDuration: 0
    };
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      controllerCount: this.controllers.size,
      masterVideoId: this.masterVideoId,
      isPlaying: this.isPlaying,
      syncInterval: this.syncIntervalMs,
      timeTolerance: this.timeTolerance,
      lastSyncTime: this.lastSyncTime,
      syncStats: { ...this.syncStats },
      controllers: Array.from(this.controllers.keys())
    };
  }

  /**
   * 设置同步参数
   */
  setSyncParameters(intervalMs, tolerance) {
    if (intervalMs && intervalMs > 0) {
      this.syncIntervalMs = intervalMs;

      // 如果定时器正在运行，重启它
      if (this.syncInterval) {
        this.stopSyncTimer();
        this.startSyncTimer();
      }
    }

    if (tolerance && tolerance > 0) {
      this.timeTolerance = tolerance;
    }

    logger.info('MultiMPVSync', `更新同步参数: 间隔=${this.syncIntervalMs}ms, 容差=${this.timeTolerance}s`);
  }

  /**
   * 检查控制器连接状态
   */
  checkControllerConnections() {
    const disconnectedControllers = [];

    for (const [videoId, controller] of this.controllers) {
      if (!controller.isConnected()) {
        disconnectedControllers.push(videoId);
      }
    }

    if (disconnectedControllers.length > 0) {
      logger.warn('MultiMPVSync', `发现断开连接的控制器: ${disconnectedControllers.join(', ')}`);
    }

    return disconnectedControllers;
  }
}

module.exports = MultiMPVSyncManager;