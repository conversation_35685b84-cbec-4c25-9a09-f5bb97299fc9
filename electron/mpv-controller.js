const net = require('net');
const logger = require('./logger');

/**
 * MPV 控制器
 * 通过 JSON IPC 协议与 MPV 进程通信
 */
class MPVController {
  constructor(ipcPath, videoId) {
    this.ipcPath = ipcPath;
    this.videoId = videoId;
    this.socket = null;
    this.connected = false;
    this.requestId = 0;
    this.pendingRequests = new Map(); // requestId -> { resolve, reject, timeout }
    this.eventListeners = new Map(); // event -> Set<callback>
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  /**
   * 连接到 MPV IPC 服务器
   */
  async connect() {
    return new Promise((resolve, reject) => {
      if (this.connected) {
        resolve();
        return;
      }

      logger.info('MPV', `连接到 MPV IPC: ${this.videoId}`);

      this.socket = net.createConnection(this.ipcPath);

      this.socket.on('connect', () => {
        logger.info('MPV', `MPV IPC 连接成功: ${this.videoId}`);
        this.connected = true;
        this.reconnectAttempts = 0;
        this.setupSocketListeners();
        resolve();
      });

      this.socket.on('error', (error) => {
        logger.error('MPV', `MPV IPC 连接错误: ${this.videoId}, ${error.message}`);
        this.connected = false;

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          reject(error);
        }
      });

      // 设置连接超时
      const timeout = setTimeout(() => {
        if (!this.connected) {
          this.socket.destroy();
          reject(new Error('连接超时'));
        }
      }, 5000);

      this.socket.on('connect', () => {
        clearTimeout(timeout);
      });
    });
  }

  /**
   * 设置套接字监听器
   */
  setupSocketListeners() {
    let buffer = '';

    this.socket.on('data', (data) => {
      buffer += data.toString();

      // 处理多个 JSON 消息
      const lines = buffer.split('\n');
      buffer = lines.pop(); // 保留不完整的行

      for (const line of lines) {
        if (line.trim()) {
          try {
            const message = JSON.parse(line);
            this.handleMessage(message);
          } catch (error) {
            logger.error('MPV', `解析 JSON 消息失败: ${error.message}, 消息: ${line}`);
          }
        }
      }
    });

    this.socket.on('close', () => {
      logger.warn('MPV', `MPV IPC 连接关闭: ${this.videoId}`);
      this.connected = false;
      this.clearPendingRequests();

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    });

    this.socket.on('error', (error) => {
      logger.error('MPV', `MPV IPC 套接字错误: ${this.videoId}, ${error.message}`);
      this.connected = false;
    });
  }

  /**
   * 处理来自 MPV 的消息
   */
  handleMessage(message) {
    if (message.request_id !== undefined) {
      // 这是对请求的响应
      const pending = this.pendingRequests.get(message.request_id);
      if (pending) {
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(message.request_id);

        if (message.error && message.error !== 'success') {
          pending.reject(new Error(message.error));
        } else {
          pending.resolve(message.data);
        }
      }
    } else if (message.event) {
      // 这是事件通知
      this.handleEvent(message.event, message);
    }
  }

  /**
   * 处理事件
   */
  handleEvent(eventName, eventData) {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(eventData);
        } catch (error) {
          logger.error('MPV', `事件监听器错误: ${error.message}`);
        }
      });
    }
  }

  /**
   * 发送命令到 MPV
   */
  async sendCommand(command, args = []) {
    if (!this.connected) {
      throw new Error('MPV IPC 未连接');
    }

    const requestId = ++this.requestId;
    const request = {
      command: [command, ...args],
      request_id: requestId
    };

    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('命令超时'));
      }, 10000);

      this.pendingRequests.set(requestId, { resolve, reject, timeout });

      try {
        this.socket.write(JSON.stringify(request) + '\n');
        logger.debug('MPV', `发送命令: ${this.videoId}, ${command}, ${JSON.stringify(args)}`);
      } catch (error) {
        clearTimeout(timeout);
        this.pendingRequests.delete(requestId);
        reject(error);
      }
    });
  }

  /**
   * 播放控制方法
   */

  // 播放
  async play() {
    return this.sendCommand('set_property', ['pause', false]);
  }

  // 暂停
  async pause() {
    return this.sendCommand('set_property', ['pause', true]);
  }

  // 切换播放/暂停
  async togglePlay() {
    return this.sendCommand('cycle', ['pause']);
  }

  // 跳转到指定时间（秒）
  async seek(time, mode = 'absolute') {
    return this.sendCommand('seek', [time, mode]);
  }

  // 设置音量（0-100）
  async setVolume(volume) {
    const clampedVolume = Math.max(0, Math.min(100, volume));
    return this.sendCommand('set_property', ['volume', clampedVolume]);
  }

  // 设置静音
  async setMute(muted) {
    return this.sendCommand('set_property', ['mute', muted]);
  }

  // 切换静音
  async toggleMute() {
    return this.sendCommand('cycle', ['mute']);
  }

  // 快进/快退（秒）
  async skipTime(seconds) {
    return this.sendCommand('seek', [seconds, 'relative']);
  }

  /**
   * 属性查询方法
   */

  // 获取当前播放时间
  async getCurrentTime() {
    return this.sendCommand('get_property', ['time-pos']);
  }

  // 获取视频总时长
  async getDuration() {
    return this.sendCommand('get_property', ['duration']);
  }

  // 获取播放状态
  async isPaused() {
    return this.sendCommand('get_property', ['pause']);
  }

  // 获取音量
  async getVolume() {
    return this.sendCommand('get_property', ['volume']);
  }

  // 获取静音状态
  async isMuted() {
    return this.sendCommand('get_property', ['mute']);
  }

  // 获取视频信息
  async getVideoInfo() {
    const [width, height, fps, format] = await Promise.all([
      this.sendCommand('get_property', ['width']).catch(() => null),
      this.sendCommand('get_property', ['height']).catch(() => null),
      this.sendCommand('get_property', ['fps']).catch(() => null),
      this.sendCommand('get_property', ['video-format']).catch(() => null)
    ]);

    return { width, height, fps, format };
  }

  /**
   * 事件监听
   */

  // 添加事件监听器
  addEventListener(eventName, callback) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, new Set());
    }
    this.eventListeners.get(eventName).add(callback);
  }

  // 移除事件监听器
  removeEventListener(eventName, callback) {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventName);
      }
    }
  }

  // 观察属性变化
  async observeProperty(propertyName) {
    return this.sendCommand('observe_property', [1, propertyName]);
  }

  // 取消观察属性
  async unobserveProperty(propertyName) {
    return this.sendCommand('unobserve_property', [1]);
  }

  /**
   * 连接管理
   */

  // 安排重连
  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    logger.info('MPV', `${delay}ms 后重连 MPV IPC: ${this.videoId} (尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch(error => {
        logger.error('MPV', `重连失败: ${error.message}`);
      });
    }, delay);
  }

  // 清理待处理的请求
  clearPendingRequests() {
    for (const [requestId, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('连接已关闭'));
    }
    this.pendingRequests.clear();
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }
    this.connected = false;
    this.clearPendingRequests();
  }

  // 检查连接状态
  isConnected() {
    return this.connected;
  }

  // 获取统计信息
  getStats() {
    return {
      videoId: this.videoId,
      connected: this.connected,
      pendingRequests: this.pendingRequests.size,
      eventListeners: this.eventListeners.size,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

module.exports = MPVController;