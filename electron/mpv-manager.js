const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { app } = require('electron');
const logger = require('./logger');

/**
 * MPV 进程管理器
 * 负责启动、控制和监听 MPV 进程，支持跨平台
 */
class MPVManager {
  constructor() {
    this.mpvProcesses = new Map(); // videoId -> { process, controller, options }
    this.mpvPaths = this.getMPVPaths();
    this.tempDir = path.join(os.tmpdir(), 'meea-mpv-sockets');
    this.ensureTempDir();
  }

  /**
   * 获取跨平台的 MPV 可执行文件路径
   */
  getMPVPaths() {
    const platform = process.platform;
    const arch = process.arch;
    const isPackaged = app.isPackaged;

    let basePath;
    if (isPackaged) {
      // 打包后的路径
      basePath = path.join(process.resourcesPath, 'mpv');
    } else {
      // 开发环境路径
      basePath = path.join(__dirname, '..', 'mpv');
    }

    const paths = {
      win32: {
        x64: path.join(basePath, 'win-x64', 'mpv.exe'),
        arm64: path.join(basePath, 'win-arm64', 'mpv.exe')
      },
      darwin: {
        x64: path.join(basePath, 'mac-x64', 'mpv'),
        arm64: path.join(basePath, 'mac-arm64', 'mpv')
      },
      linux: {
        x64: path.join(basePath, 'linux-x64', 'mpv'),
        arm64: path.join(basePath, 'linux-arm64', 'mpv')
      }
    };

    logger.info('MPV', `平台: ${platform}, 架构: ${arch}`);
    logger.info('MPV', `MPV路径: ${paths[platform]?.[arch] || 'NOT_FOUND'}`);

    return paths;
  }

  /**
   * 确保临时目录存在
   */
  ensureTempDir() {
    try {
      if (!fs.existsSync(this.tempDir)) {
        fs.mkdirSync(this.tempDir, { recursive: true });
      }
    } catch (error) {
      logger.error('MPV', `创建临时目录失败: ${error.message}`);
    }
  }

  /**
   * 获取 MPV 可执行文件路径
   */
  getMPVExecutablePath() {
    const platform = process.platform;
    const arch = process.arch;

    const mpvPath = this.mpvPaths[platform]?.[arch];
    if (!mpvPath) {
      throw new Error(`不支持的平台: ${platform}-${arch}`);
    }

    if (!fs.existsSync(mpvPath)) {
      throw new Error(`MPV 可执行文件不存在: ${mpvPath}`);
    }

    return mpvPath;
  }

  /**
   * 生成 IPC 套接字路径
   */
  getIPCPath(videoId) {
    const platform = process.platform;

    if (platform === 'win32') {
      // Windows 使用命名管道
      return `\\\\.\\pipe\\mpv-${videoId}`;
    } else {
      // Unix 系统使用 Unix 套接字
      return path.join(this.tempDir, `mpv-${videoId}.sock`);
    }
  }

  /**
   * 启动 MPV 进程
   */
  async startMPV(videoId, videoPath, windowHandle, options = {}) {
    try {
      // 检查是否已经存在该视频的进程
      if (this.mpvProcesses.has(videoId)) {
        logger.warn('MPV', `视频 ${videoId} 的 MPV 进程已存在`);
        return this.mpvProcesses.get(videoId);
      }

      const mpvPath = this.getMPVExecutablePath();
      const ipcPath = this.getIPCPath(videoId);

      // 构建 MPV 启动参数
      const args = [
        '--no-terminal',           // 不显示终端
        '--idle=yes',              // 保持空闲状态
        '--no-osc',                // 禁用内置控制界面
        '--no-input-default-bindings', // 禁用默认键盘绑定
        '--input-ipc-server=' + ipcPath, // IPC 服务器
        '--wid=' + windowHandle,   // 嵌入窗口句柄
        '--keep-open=yes',         // 播放结束后保持打开
        '--pause=yes',             // 启动时暂停
        '--volume=100',            // 默认音量
        '--mute=no',               // 默认不静音
        ...Object.entries(options).map(([key, value]) => `--${key}=${value}`),
        videoPath                  // 视频文件路径
      ];

      logger.info('MPV', `启动 MPV 进程: ${videoId}`);
      logger.info('MPV', `命令: ${mpvPath} ${args.join(' ')}`);

      // 启动 MPV 进程
      const mpvProcess = spawn(mpvPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: false
      });

      // 设置进程信息
      const processInfo = {
        process: mpvProcess,
        videoId,
        videoPath,
        ipcPath,
        windowHandle,
        startTime: Date.now(),
        options
      };

      this.mpvProcesses.set(videoId, processInfo);

      // 监听进程事件
      this.setupProcessListeners(videoId, mpvProcess);

      logger.info('MPV', `MPV 进程启动成功: ${videoId}, PID: ${mpvProcess.pid}`);

      return processInfo;

    } catch (error) {
      logger.error('MPV', `启动 MPV 进程失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 设置进程监听器
   */
  setupProcessListeners(videoId, mpvProcess) {
    mpvProcess.on('spawn', () => {
      logger.info('MPV', `MPV 进程已启动: ${videoId}, PID: ${mpvProcess.pid}`);
    });

    mpvProcess.on('error', (error) => {
      logger.error('MPV', `MPV 进程错误: ${videoId}, ${error.message}`);
      this.mpvProcesses.delete(videoId);
    });

    mpvProcess.on('exit', (code, signal) => {
      logger.info('MPV', `MPV 进程退出: ${videoId}, 代码: ${code}, 信号: ${signal}`);
      this.mpvProcesses.delete(videoId);
    });

    // 监听标准输出和错误输出
    mpvProcess.stdout.on('data', (data) => {
      logger.debug('MPV', `${videoId} stdout: ${data.toString().trim()}`);
    });

    mpvProcess.stderr.on('data', (data) => {
      logger.debug('MPV', `${videoId} stderr: ${data.toString().trim()}`);
    });
  }

  /**
   * 停止 MPV 进程
   */
  async stopMPV(videoId) {
    try {
      const processInfo = this.mpvProcesses.get(videoId);
      if (!processInfo) {
        logger.warn('MPV', `视频 ${videoId} 的 MPV 进程不存在`);
        return false;
      }

      const { process: mpvProcess, ipcPath } = processInfo;

      logger.info('MPV', `停止 MPV 进程: ${videoId}, PID: ${mpvProcess.pid}`);

      // 尝试优雅关闭
      mpvProcess.kill('SIGTERM');

      // 等待进程退出，如果超时则强制杀死
      const timeout = setTimeout(() => {
        if (!mpvProcess.killed) {
          logger.warn('MPV', `强制杀死 MPV 进程: ${videoId}`);
          mpvProcess.kill('SIGKILL');
        }
      }, 5000);

      mpvProcess.on('exit', () => {
        clearTimeout(timeout);
        this.cleanupSocketFile(ipcPath);
      });

      this.mpvProcesses.delete(videoId);
      return true;

    } catch (error) {
      logger.error('MPV', `停止 MPV 进程失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 清理套接字文件
   */
  cleanupSocketFile(ipcPath) {
    try {
      if (process.platform !== 'win32' && fs.existsSync(ipcPath)) {
        fs.unlinkSync(ipcPath);
        logger.debug('MPV', `清理套接字文件: ${ipcPath}`);
      }
    } catch (error) {
      logger.error('MPV', `清理套接字文件失败: ${error.message}`);
    }
  }

  /**
   * 获取进程信息
   */
  getProcessInfo(videoId) {
    return this.mpvProcesses.get(videoId);
  }

  /**
   * 获取所有活动进程
   */
  getAllProcesses() {
    return Array.from(this.mpvProcesses.values());
  }

  /**
   * 检查进程是否存在
   */
  hasProcess(videoId) {
    return this.mpvProcesses.has(videoId);
  }

  /**
   * 重启 MPV 进程
   */
  async restartMPV(videoId) {
    try {
      const processInfo = this.mpvProcesses.get(videoId);
      if (!processInfo) {
        throw new Error(`视频 ${videoId} 的 MPV 进程不存在`);
      }

      const { videoPath, windowHandle, options } = processInfo;

      logger.info('MPV', `重启 MPV 进程: ${videoId}`);

      // 停止现有进程
      await this.stopMPV(videoId);

      // 等待一段时间确保进程完全退出
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 重新启动进程
      return await this.startMPV(videoId, videoPath, windowHandle, options);

    } catch (error) {
      logger.error('MPV', `重启 MPV 进程失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理所有进程
   */
  async cleanup() {
    logger.info('MPV', '清理所有 MPV 进程');

    const promises = Array.from(this.mpvProcesses.keys()).map(videoId =>
      this.stopMPV(videoId)
    );

    await Promise.all(promises);

    // 清理临时目录
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        for (const file of files) {
          const filePath = path.join(this.tempDir, file);
          if (file.startsWith('mpv-') && file.endsWith('.sock')) {
            fs.unlinkSync(filePath);
          }
        }
      }
    } catch (error) {
      logger.error('MPV', `清理临时目录失败: ${error.message}`);
    }
  }

  /**
   * 获取进程统计信息
   */
  getStats() {
    const processes = this.getAllProcesses();
    return {
      totalProcesses: processes.length,
      processes: processes.map(info => ({
        videoId: info.videoId,
        pid: info.process.pid,
        uptime: Date.now() - info.startTime,
        videoPath: path.basename(info.videoPath)
      }))
    };
  }
}

// 创建单例实例
const mpvManager = new MPVManager();

module.exports = mpvManager;