const logger = require('./logger');

/**
 * 窗口句柄管理器
 * 处理跨平台的窗口句柄获取和 MPV 嵌入
 */
class WindowHandleManager {

  /**
   * 获取窗口句柄（跨平台）
   * @param {BrowserWindow} browserWindow - Electron 浏览器窗口
   * @returns {string|number} 窗口句柄
   */
  static getWindowHandle(browserWindow) {
    try {
      const platform = process.platform;
      const nativeHandle = browserWindow.getNativeWindowHandle();

      let windowHandle;

      switch (platform) {
        case 'win32':
          // Windows: 读取 HWND
          windowHandle = nativeHandle.readUInt32LE(0);
          break;

        case 'darwin':
          // macOS: 读取 NSView 指针
          if (process.arch === 'arm64') {
            windowHandle = nativeHandle.readBigUInt64LE(0).toString();
          } else {
            windowHandle = nativeHandle.readUInt32LE(0);
          }
          break;

        case 'linux':
          // Linux: 读取 X11 Window ID
          windowHandle = nativeHandle.readUInt32LE(0);
          break;

        default:
          throw new Error(`不支持的平台: ${platform}`);
      }

      logger.info('WindowHandle', `获取窗口句柄成功: ${platform}, 句柄: ${windowHandle}`);
      return windowHandle;

    } catch (error) {
      logger.error('WindowHandle', `获取窗口句柄失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证窗口句柄是否有效
   * @param {string|number} windowHandle - 窗口句柄
   * @returns {boolean} 是否有效
   */
  static isValidWindowHandle(windowHandle) {
    if (windowHandle === null || windowHandle === undefined) {
      return false;
    }

    const platform = process.platform;

    switch (platform) {
      case 'win32':
        // Windows HWND 应该是正整数
        return typeof windowHandle === 'number' && windowHandle > 0;

      case 'darwin':
        // macOS NSView 指针应该是正数
        return (typeof windowHandle === 'number' && windowHandle > 0) ||
               (typeof windowHandle === 'string' && parseInt(windowHandle) > 0);

      case 'linux':
        // Linux X11 Window ID 应该是正整数
        return typeof windowHandle === 'number' && windowHandle > 0;

      default:
        return false;
    }
  }

  /**
   * 创建 MPV 嵌入容器的 HTML 元素
   * @param {string} videoId - 视频 ID
   * @returns {string} HTML 字符串
   */
  static createEmbedContainerHTML(videoId) {
    return `
      <div
        id="mpv-container-${videoId}"
        class="mpv-container"
        style="
          width: 100%;
          height: 100%;
          background-color: #000;
          position: relative;
          overflow: hidden;
        "
      >
        <div
          class="mpv-loading"
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            opacity: 0.7;
          "
        >
          正在加载视频...
        </div>
      </div>
    `;
  }

  /**
   * 获取平台特定的 MPV 嵌入参数
   * @param {string|number} windowHandle - 窗口句柄
   * @returns {object} MPV 参数对象
   */
  static getMPVEmbedOptions(windowHandle) {
    const platform = process.platform;
    const options = {};

    switch (platform) {
      case 'win32':
        options.wid = windowHandle;
        // Windows 特定选项
        options['vo'] = 'gpu';
        options['gpu-context'] = 'angle';
        break;

      case 'darwin':
        options.wid = windowHandle;
        // macOS 特定选项
        options['vo'] = 'libmpv';
        options['cocoa-force-dedicated-gpu'] = 'yes';
        break;

      case 'linux':
        options.wid = windowHandle;
        // Linux 特定选项
        options['vo'] = 'gpu';
        options['gpu-context'] = 'x11egl';
        break;
    }

    return options;
  }

  /**
   * 检查平台是否支持窗口嵌入
   * @returns {boolean} 是否支持
   */
  static isPlatformSupported() {
    const platform = process.platform;
    return ['win32', 'darwin', 'linux'].includes(platform);
  }

  /**
   * 获取平台信息
   * @returns {object} 平台信息
   */
  static getPlatformInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      supported: this.isPlatformSupported(),
      features: {
        windowEmbedding: this.isPlatformSupported(),
        hardwareAcceleration: true,
        multipleInstances: true
      }
    };
  }

  /**
   * 创建用于测试的虚拟窗口句柄
   * @returns {number} 测试用的窗口句柄
   */
  static createTestWindowHandle() {
    const platform = process.platform;

    switch (platform) {
      case 'win32':
        return 0x12345678; // 虚拟 HWND
      case 'darwin':
        return 0x87654321; // 虚拟 NSView
      case 'linux':
        return 0x11223344; // 虚拟 X11 Window ID
      default:
        return 0;
    }
  }

  /**
   * 日志窗口句柄信息
   * @param {string|number} windowHandle - 窗口句柄
   * @param {string} context - 上下文信息
   */
  static logWindowHandleInfo(windowHandle, context = '') {
    const platform = process.platform;
    const arch = process.arch;
    const valid = this.isValidWindowHandle(windowHandle);

    logger.info('WindowHandle', `${context} - 平台: ${platform}-${arch}, 句柄: ${windowHandle}, 有效: ${valid}`);
  }
}

module.exports = WindowHandleManager;