#!/usr/bin/env node

/**
 * 检查构建架构信息
 * 用于诊断为什么 Windows 构建显示为 32 位
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查构建架构信息...\n');

// 1. 检查 Node.js 和进程信息
console.log('📋 Node.js 和进程信息:');
console.log(`  - Node.js 版本: ${process.version}`);
console.log(`  - 平台: ${process.platform}`);
console.log(`  - 架构: ${process.arch}`);
console.log(`  - 执行路径: ${process.execPath}`);
console.log('');

// 2. 检查 Electron 信息
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log('📋 Electron 信息:');
  console.log(`  - Electron 版本: ${packageJson.devDependencies.electron}`);
  console.log(`  - electron-builder 版本: ${packageJson.devDependencies['electron-builder']}`);
  console.log('');
} catch (error) {
  console.log('⚠️  无法读取 package.json');
}

// 3. 检查构建配置
const configFiles = [
  'electron-builder-win-x64.json',
  'electron-builder-win-arm64.json'
];

console.log('📋 构建配置检查:');
for (const configFile of configFiles) {
  if (fs.existsSync(configFile)) {
    try {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      console.log(`  - ${configFile}:`);
      
      if (config.win && config.win.target) {
        config.win.target.forEach((target, index) => {
          console.log(`    目标 ${index + 1}: ${target.target} - 架构: ${target.arch.join(', ')}`);
        });
      }
      
      if (config.nsis && config.nsis.artifactName) {
        console.log(`    安装包名称: ${config.nsis.artifactName}`);
      }
    } catch (error) {
      console.log(`    ❌ 无法解析 ${configFile}: ${error.message}`);
    }
  } else {
    console.log(`  - ${configFile}: 不存在`);
  }
}
console.log('');

// 4. 检查构建产物
console.log('📋 构建产物检查:');
const distDir = 'dist';
if (fs.existsSync(distDir)) {
  const files = fs.readdirSync(distDir);
  const windowsFiles = files.filter(file => 
    file.includes('windows') || file.includes('win') || file.endsWith('.exe')
  );
  
  if (windowsFiles.length > 0) {
    console.log('  Windows 构建产物:');
    windowsFiles.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      console.log(`    - ${file} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    });
  } else {
    console.log('  - 未找到 Windows 构建产物');
  }
} else {
  console.log('  - dist 目录不存在');
}
console.log('');

// 5. 检查可能的问题
console.log('🔍 可能的问题诊断:');

// 检查是否有 32 位相关的配置
const possibleIssues = [];

// 检查 package.json 中是否有 32 位相关配置
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.build && packageJson.build.win) {
    const winConfig = packageJson.build.win;
    if (winConfig.target) {
      winConfig.target.forEach(target => {
        if (target.arch && target.arch.includes('ia32')) {
          possibleIssues.push('package.json 中的 win.target 包含 ia32 架构');
        }
      });
    }
  }
} catch (error) {
  // 忽略错误
}

// 检查环境变量
if (process.env.npm_config_target_arch === 'ia32') {
  possibleIssues.push('环境变量 npm_config_target_arch 设置为 ia32');
}

if (process.env.npm_config_arch === 'ia32') {
  possibleIssues.push('环境变量 npm_config_arch 设置为 ia32');
}

if (possibleIssues.length > 0) {
  console.log('  发现可能的问题:');
  possibleIssues.forEach(issue => {
    console.log(`    ❌ ${issue}`);
  });
} else {
  console.log('  ✅ 未发现明显的配置问题');
}

console.log('');

// 6. 建议的解决方案
console.log('💡 建议的解决方案:');
console.log('  1. 确认构建命令使用了正确的配置文件');
console.log('  2. 检查 electron-builder 是否正确识别了架构');
console.log('  3. 清理 node_modules 和重新安装依赖');
console.log('  4. 检查构建产物的实际架构信息');
console.log('');

console.log('🔧 调试命令:');
console.log('  - 查看详细构建日志: yarn build:windows-x64 --verbose');
console.log('  - 清理并重新构建: rm -rf node_modules dist && yarn install && yarn build:windows-x64');
console.log('  - 检查构建产物架构: file dist/win-unpacked/MEEA-VIOFO.exe (Linux/macOS)');
console.log('  - 或在 Windows 上右键查看属性');
