#!/usr/bin/env node

/**
 * Windows 构建资源验证脚本
 * 验证 Windows 构建所需的资源是否存在
 * 不删除任何文件，保留其他平台构建所需的资源
 */

const fs = require('fs');
const path = require('path');

// 从命令行参数获取架构
const arch = process.argv[2] || 'x64';
console.log(`🔍 验证 Windows ${arch.toUpperCase()} 构建资源...`);

// 验证关键的 Windows 资源是否存在
const requiredWindowsResources = [
  {
    path: `ffmpeg/win-${arch}`,
    description: `FFmpeg Windows ${arch.toUpperCase()} 二进制文件`
  },
  {
    path: `exiftool/win-${arch}`,
    description: `ExifTool Windows ${arch.toUpperCase()} 二进制文件`
  },
  {
    path: 'build/icons/icon.ico',
    description: 'Windows 应用图标'
  },
  {
    path: 'electron/main.js',
    description: 'Electron 主进程文件'
  },
  {
    path: 'dist/index.html',
    description: '前端构建产物'
  }
];

let allResourcesPresent = true;
let criticalMissing = [];

console.log('\n📋 检查必需资源...');

for (const resource of requiredWindowsResources) {
  if (fs.existsSync(resource.path)) {
    console.log(`✅ ${resource.description} - ${resource.path}`);
  } else {
    console.log(`❌ ${resource.description} - ${resource.path} (缺失)`);
    criticalMissing.push(resource);
    allResourcesPresent = false;
  }
}

// 显示构建配置信息
console.log('\n📋 构建配置信息...');
console.log(`✅ 构建目标: Windows ${arch.toUpperCase()} only`);
console.log(`✅ 调试模式: 生产环境关闭`);
console.log(`✅ 资源过滤: 通过 electron-builder 配置`);

// 显示将被排除的资源（不删除，只是不打包）
console.log('\n📋 将被排除的资源 (不打包，但保留在文件系统中)...');
const excludedPatterns = [
  'ffmpeg/mac-*/**/*',
  'ffmpeg/linux-*/**/*',
  arch === 'x64' ? 'ffmpeg/win-arm64/**/*' : 'ffmpeg/win-x64/**/*',
  'exiftool/mac-*/**/*',
  'exiftool/linux-*/**/*',
  arch === 'x64' ? 'exiftool/win-arm64/**/*' : 'exiftool/win-x64/**/*',
  'node_modules/**/prebuilds/darwin-*/**/*',
  'node_modules/**/prebuilds/linux-*/**/*',
  arch === 'x64' ? 'node_modules/**/prebuilds/win32-arm64/**/*' : 'node_modules/**/prebuilds/win32-x64/**/*'
];

excludedPatterns.forEach(pattern => {
  console.log(`🚫 ${pattern}`);
});

console.log('\n🎉 资源验证完成!');

if (criticalMissing.length > 0) {
  console.log(`\n❌ 发现 ${criticalMissing.length} 个关键资源缺失:`);
  criticalMissing.forEach(resource => {
    console.log(`   - ${resource.description}: ${resource.path}`);
  });
  console.log('\n请确保运行了前置构建步骤：');
  console.log('   yarn predist && yarn build');
  console.log('\n并且确保已下载对应平台的二进制文件：');
  console.log(`   - FFmpeg Windows ${arch.toUpperCase()}`);
  console.log(`   - ExifTool Windows ${arch.toUpperCase()}`);
  process.exit(1);
}

console.log(`\n✅ 所有关键资源都已就位，可以开始 Windows ${arch.toUpperCase()} 构建!`);
console.log('💡 提示: 其他平台的资源已保留，不会影响后续的多平台构建');

// 显示将被包含的资源
console.log('\n📦 将被包含的资源...');
console.log(`✅ ffmpeg/win-${arch}/**/*`);
console.log(`✅ exiftool/win-${arch}/**/*`);
console.log(`✅ node_modules/**/prebuilds/win32-${arch}/**/* (如果存在)`);
console.log(`✅ Windows ${arch.toUpperCase()} 相关的原生模块`);
