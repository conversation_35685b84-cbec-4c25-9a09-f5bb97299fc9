#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

/**
 * MPV 二进制文件下载和设置脚本
 * 支持 Windows、macOS、Linux 三平台的 x64 和 arm64 架构
 */

const MPV_VERSIONS = {
  windows: '0.37.0',
  macos: '0.37.0',
  linux: '0.37.0'
};

const MPV_DOWNLOAD_URLS = {
  'win-x64': `https://sourceforge.net/projects/mpv-player-windows/files/64bit/mpv-x86_64-${MPV_VERSIONS.windows}.7z/download`,
  'win-arm64': `https://sourceforge.net/projects/mpv-player-windows/files/64bit-v3/mpv-x86_64-${MPV_VERSIONS.windows}.7z/download`, // 注意：Windows ARM64 版本可能需要特殊处理
  'mac-x64': `https://laboratory.stolendata.net/~djinn/mpv_osx/mpv-${MPV_VERSIONS.macos}-x86_64.tar.gz`,
  'mac-arm64': `https://laboratory.stolendata.net/~djinn/mpv_osx/mpv-${MPV_VERSIONS.macos}-arm64.tar.gz`,
  'linux-x64': `https://github.com/mpv-player/mpv/releases/download/v${MPV_VERSIONS.linux}/mpv-${MPV_VERSIONS.linux}-x86_64.AppImage`,
  'linux-arm64': `https://github.com/mpv-player/mpv/releases/download/v${MPV_VERSIONS.linux}/mpv-${MPV_VERSIONS.linux}-aarch64.AppImage`
};

const PROJECT_ROOT = path.resolve(__dirname, '..');
const MPV_DIR = path.join(PROJECT_ROOT, 'mpv');

/**
 * 确保目录存在
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dirPath}`);
  }
}

/**
 * 下载文件
 */
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`📥 下载: ${url}`);
    console.log(`📁 保存到: ${outputPath}`);

    const file = fs.createWriteStream(outputPath);

    https.get(url, (response) => {
      // 处理重定向
      if (response.statusCode === 302 || response.statusCode === 301) {
        file.close();
        fs.unlinkSync(outputPath);
        return downloadFile(response.headers.location, outputPath)
          .then(resolve)
          .catch(reject);
      }

      if (response.statusCode !== 200) {
        file.close();
        fs.unlinkSync(outputPath);
        return reject(new Error(`下载失败: HTTP ${response.statusCode}`));
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        console.log(`✅ 下载完成: ${path.basename(outputPath)}`);
        resolve();
      });

      file.on('error', (err) => {
        file.close();
        fs.unlinkSync(outputPath);
        reject(err);
      });

    }).on('error', (err) => {
      file.close();
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
      reject(err);
    });
  });
}

/**
 * 解压文件
 */
function extractFile(archivePath, extractDir, platform, arch) {
  console.log(`📦 解压: ${archivePath} -> ${extractDir}`);

  try {
    if (archivePath.endsWith('.7z')) {
      // Windows 7z 文件
      execSync(`7z x "${archivePath}" -o"${extractDir}" -y`, { stdio: 'inherit' });
    } else if (archivePath.endsWith('.tar.gz')) {
      // macOS tar.gz 文件
      execSync(`tar -xzf "${archivePath}" -C "${extractDir}"`, { stdio: 'inherit' });
    } else if (archivePath.endsWith('.AppImage')) {
      // Linux AppImage 文件 - 直接复制并设置执行权限
      const targetPath = path.join(extractDir, 'mpv');
      fs.copyFileSync(archivePath, targetPath);
      fs.chmodSync(targetPath, 0o755);
      console.log(`✅ AppImage 复制完成: ${targetPath}`);
      return;
    }

    console.log(`✅ 解压完成: ${extractDir}`);
  } catch (error) {
    console.error(`❌ 解压失败: ${error.message}`);
    throw error;
  }
}

/**
 * 整理 MPV 文件结构
 */
function organizeMPVFiles(extractDir, targetDir, platform, arch) {
  console.log(`📁 整理文件: ${extractDir} -> ${targetDir}`);

  ensureDir(targetDir);

  if (platform === 'win') {
    // Windows: 查找 mpv.exe
    const findMPVExe = (dir) => {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        if (fs.statSync(itemPath).isDirectory()) {
          const result = findMPVExe(itemPath);
          if (result) return result;
        } else if (item === 'mpv.exe') {
          return itemPath;
        }
      }
      return null;
    };

    const mpvExePath = findMPVExe(extractDir);
    if (mpvExePath) {
      const targetPath = path.join(targetDir, 'mpv.exe');
      fs.copyFileSync(mpvExePath, targetPath);
      console.log(`✅ 复制 mpv.exe: ${targetPath}`);
    } else {
      throw new Error('未找到 mpv.exe');
    }

  } else if (platform === 'mac') {
    // macOS: 查找 mpv 可执行文件
    const findMPVBinary = (dir) => {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const itemPath = path.join(dir, item);
        if (fs.statSync(itemPath).isDirectory()) {
          const result = findMPVBinary(itemPath);
          if (result) return result;
        } else if (item === 'mpv' && fs.statSync(itemPath).mode & 0o111) {
          return itemPath;
        }
      }
      return null;
    };

    const mpvBinaryPath = findMPVBinary(extractDir);
    if (mpvBinaryPath) {
      const targetPath = path.join(targetDir, 'mpv');
      fs.copyFileSync(mpvBinaryPath, targetPath);
      fs.chmodSync(targetPath, 0o755);
      console.log(`✅ 复制 mpv: ${targetPath}`);
    } else {
      throw new Error('未找到 mpv 可执行文件');
    }

  } else if (platform === 'linux') {
    // Linux: AppImage 已经在解压时处理
    console.log(`✅ Linux AppImage 已处理完成`);
  }
}

/**
 * 设置单个平台的 MPV
 */
async function setupMPVForPlatform(platform, arch) {
  const platformKey = `${platform}-${arch}`;
  const url = MPV_DOWNLOAD_URLS[platformKey];

  if (!url) {
    console.log(`⚠️  跳过不支持的平台: ${platformKey}`);
    return;
  }

  console.log(`\n🚀 设置 MPV for ${platformKey}`);

  const platformDir = path.join(MPV_DIR, platformKey);
  const tempDir = path.join(MPV_DIR, 'temp', platformKey);

  ensureDir(platformDir);
  ensureDir(tempDir);

  // 检查是否已存在
  const expectedBinary = platform === 'win' ? 'mpv.exe' : 'mpv';
  const binaryPath = path.join(platformDir, expectedBinary);

  if (fs.existsSync(binaryPath)) {
    console.log(`✅ MPV 已存在: ${binaryPath}`);
    return;
  }

  try {
    // 下载
    const fileName = url.split('/').pop().split('?')[0] || `mpv-${platformKey}`;
    const downloadPath = path.join(tempDir, fileName);

    await downloadFile(url, downloadPath);

    // 解压
    const extractDir = path.join(tempDir, 'extracted');
    ensureDir(extractDir);
    extractFile(downloadPath, extractDir, platform, arch);

    // 整理文件
    organizeMPVFiles(extractDir, platformDir, platform, arch);

    // 清理临时文件
    fs.rmSync(tempDir, { recursive: true, force: true });

    console.log(`✅ ${platformKey} MPV 设置完成`);

  } catch (error) {
    console.error(`❌ ${platformKey} MPV 设置失败:`, error.message);

    // 清理临时文件
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }

    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🎬 开始设置 MPV 二进制文件...\n');

  // 确保 MPV 目录存在
  ensureDir(MPV_DIR);

  const platforms = [
    ['win', 'x64'],
    ['win', 'arm64'],
    ['mac', 'x64'],
    ['mac', 'arm64'],
    ['linux', 'x64'],
    ['linux', 'arm64']
  ];

  let successCount = 0;
  let failCount = 0;

  for (const [platform, arch] of platforms) {
    try {
      await setupMPVForPlatform(platform, arch);
      successCount++;
    } catch (error) {
      console.error(`❌ ${platform}-${arch} 失败:`, error.message);
      failCount++;
    }
  }

  console.log(`\n📊 设置完成:`);
  console.log(`✅ 成功: ${successCount}`);
  console.log(`❌ 失败: ${failCount}`);

  if (failCount > 0) {
    console.log(`\n⚠️  部分平台设置失败，但不影响支持的平台使用`);
    console.log(`💡 提示: 可以手动下载对应平台的 MPV 二进制文件到 mpv/ 目录`);
  }

  console.log(`\n🎉 MPV 设置脚本执行完成！`);
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { setupMPVForPlatform, main };