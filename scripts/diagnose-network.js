#!/usr/bin/env node

/**
 * 网络连接诊断脚本
 * 检查各种镜像源的连接状态
 */

const https = require('https');
const http = require('http');

// 测试的镜像源
const mirrors = [
  {
    name: 'GitHub Releases (官方)',
    url: 'https://github.com/electron/electron/releases'
  },
  {
    name: 'NPM 镜像 (npmmirror)',
    url: 'https://npmmirror.com/mirrors/electron/'
  },
  {
    name: 'NPM 官方源',
    url: 'https://registry.npmjs.org/'
  },
  {
    name: 'NPM 镜像源',
    url: 'https://registry.npmmirror.com/'
  }
];

function testConnection(url, timeout = 10000) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      resolve({
        success: true,
        statusCode: res.statusCode,
        responseTime,
        error: null
      });
      
      req.destroy();
    });
    
    req.on('error', (error) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      resolve({
        success: false,
        statusCode: null,
        responseTime,
        error: error.message
      });
    });
    
    req.setTimeout(timeout, () => {
      resolve({
        success: false,
        statusCode: null,
        responseTime: timeout,
        error: 'Timeout'
      });
      req.destroy();
    });
  });
}

async function diagnoseMirrors() {
  console.log('🔍 网络连接诊断开始...\n');
  
  for (const mirror of mirrors) {
    console.log(`测试: ${mirror.name}`);
    console.log(`URL: ${mirror.url}`);
    
    const result = await testConnection(mirror.url);
    
    if (result.success) {
      console.log(`✅ 连接成功 (${result.responseTime}ms, HTTP ${result.statusCode})`);
    } else {
      console.log(`❌ 连接失败: ${result.error} (${result.responseTime}ms)`);
    }
    console.log('');
  }
  
  // 推荐配置
  console.log('💡 推荐配置:');
  console.log('');
  console.log('如果 npmmirror 连接正常，使用以下配置:');
  console.log('export ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"');
  console.log('export ELECTRON_CUSTOM_DIR="{{ version }}"');
  console.log('');
  console.log('或者创建 .yarnrc 文件:');
  console.log('electron_mirror "https://npmmirror.com/mirrors/electron/"');
  console.log('electron_custom_dir "{{ version }}"');
  console.log('registry "https://registry.npmmirror.com"');
  console.log('');
  console.log('然后运行: yarn install');
}

// 运行诊断
diagnoseMirrors().catch(console.error);
