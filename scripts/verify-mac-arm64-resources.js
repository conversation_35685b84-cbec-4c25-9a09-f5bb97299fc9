#!/usr/bin/env node

/**
 * macOS ARM64 构建资源验证脚本
 * 验证 ARM64 构建所需的资源是否存在
 * 不删除任何文件，保留其他平台构建所需的资源
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 macOS ARM64 构建资源...');

// 验证关键的 ARM64 资源是否存在
const requiredARM64Resources = [
  {
    path: 'ffmpeg/mac-arm64',
    description: 'FFmpeg ARM64 二进制文件'
  },
  {
    path: 'build/icons/icon.icns',
    description: 'macOS 应用图标'
  },
  {
    path: 'electron/main.js',
    description: 'Electron 主进程文件'
  },
  {
    path: 'dist/index.html',
    description: '前端构建产物'
  }
];

// 检查可选的 ARM64 原生模块
const optionalARM64Resources = [
  {
    pattern: 'node_modules/**/prebuilds/darwin-arm64',
    description: 'ARM64 原生模块预构建文件'
  }
];

let allResourcesPresent = true;
let criticalMissing = [];
let optionalMissing = [];

console.log('\n📋 检查必需资源...');

for (const resource of requiredARM64Resources) {
  if (fs.existsSync(resource.path)) {
    console.log(`✅ ${resource.description} - ${resource.path}`);
  } else {
    console.log(`❌ ${resource.description} - ${resource.path} (缺失)`);
    criticalMissing.push(resource);
    allResourcesPresent = false;
  }
}

// 使用简单的 glob 匹配检查可选资源
function findMatchingPaths(pattern) {
  const results = [];
  const parts = pattern.split('/');
  
  function searchRecursive(currentPath, patternParts, index = 0) {
    if (index >= patternParts.length) {
      return [currentPath];
    }
    
    const currentPart = patternParts[index];
    const searchResults = [];
    
    if (currentPart.includes('*')) {
      if (!fs.existsSync(currentPath)) return searchResults;
      
      try {
        const entries = fs.readdirSync(currentPath, { withFileTypes: true });
        const regex = new RegExp('^' + currentPart.replace(/\*/g, '.*') + '$');
        
        for (const entry of entries) {
          if (regex.test(entry.name)) {
            const nextPath = path.join(currentPath, entry.name);
            if (index === patternParts.length - 1) {
              searchResults.push(nextPath);
            } else {
              searchResults.push(...searchRecursive(nextPath, patternParts, index + 1));
            }
          }
        }
      } catch (error) {
        // 忽略权限错误等
      }
    } else {
      const nextPath = path.join(currentPath, currentPart);
      if (index === patternParts.length - 1) {
        if (fs.existsSync(nextPath)) {
          searchResults.push(nextPath);
        }
      } else {
        searchResults.push(...searchRecursive(nextPath, patternParts, index + 1));
      }
    }
    
    return searchResults;
  }
  
  return searchRecursive('.', parts);
}

console.log('\n📋 检查可选资源...');

for (const resource of optionalARM64Resources) {
  const matches = findMatchingPaths(resource.pattern);
  if (matches.length > 0) {
    console.log(`✅ ${resource.description} - 找到 ${matches.length} 个匹配项`);
    matches.slice(0, 3).forEach(match => {
      console.log(`   📁 ${match}`);
    });
    if (matches.length > 3) {
      console.log(`   ... 还有 ${matches.length - 3} 个`);
    }
  } else {
    console.log(`⚠️  ${resource.description} - 未找到匹配项 (可选)`);
    optionalMissing.push(resource);
  }
}

// 显示构建配置信息
console.log('\n📋 构建配置信息...');
console.log(`✅ 构建目标: macOS ARM64 only`);
console.log(`✅ 调试模式: 生产环境关闭`);
console.log(`✅ 资源过滤: 通过 electron-builder 配置`);

// 显示将被排除的资源（不删除，只是不打包）
console.log('\n📋 将被排除的资源 (不打包，但保留在文件系统中)...');
const excludedPatterns = [
  'ffmpeg/mac-x64/**/*',
  'ffmpeg/win-*/**/*',
  'ffmpeg/linux-*/**/*',
  'node_modules/**/prebuilds/darwin-x64/**/*',
  'node_modules/**/prebuilds/win32-*/**/*',
  'node_modules/**/prebuilds/linux-*/**/*'
];

excludedPatterns.forEach(pattern => {
  console.log(`🚫 ${pattern}`);
});

console.log('\n🎉 资源验证完成!');

if (criticalMissing.length > 0) {
  console.log(`\n❌ 发现 ${criticalMissing.length} 个关键资源缺失:`);
  criticalMissing.forEach(resource => {
    console.log(`   - ${resource.description}: ${resource.path}`);
  });
  console.log('\n请确保运行了前置构建步骤：');
  console.log('   yarn predist && yarn build');
  process.exit(1);
}

if (optionalMissing.length > 0) {
  console.log(`\n⚠️  发现 ${optionalMissing.length} 个可选资源缺失 (不影响构建):`);
  optionalMissing.forEach(resource => {
    console.log(`   - ${resource.description}`);
  });
}

console.log('\n✅ 所有关键资源都已就位，可以开始 ARM64 构建!');
console.log('💡 提示: 其他平台的资源已保留，不会影响后续的多平台构建');
