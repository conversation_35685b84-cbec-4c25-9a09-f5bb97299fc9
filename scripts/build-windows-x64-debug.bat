@echo off
REM Windows x64 调试构建脚本
REM 此脚本会设置调试环境变量并构建 Windows x64 版本，启用控制台调试

echo 🚀 Windows x64 调试构建
echo ======================

REM 设置调试环境变量
set MEEA_WINDOWS_X64_DEBUG=true
set DEBUG_MODE=true

echo 📋 环境变量设置:
echo   MEEA_WINDOWS_X64_DEBUG: %MEEA_WINDOWS_X64_DEBUG%
echo   DEBUG_MODE: %DEBUG_MODE%
echo.

echo 🔧 开始构建...
yarn build:windows-x64

echo.
echo ✅ 构建完成！
echo.
echo 📝 注意事项:
echo   - 此版本启用了开发者工具
echo   - 应用启动时会自动打开控制台
echo   - 便于调试和问题排查
echo   - 仅用于调试目的，不适合正式发布
echo.
echo 🎯 构建产物:
echo   文件: dist\MEEA-VIOFO-Setup-*-windows-x64.exe
echo   特性: 包含开发者工具和控制台输出

pause
