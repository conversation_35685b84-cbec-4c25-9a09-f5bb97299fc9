<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/document.icns</key>
		<data>
		d0HCFnNAzAcI7tSAc8APtdoeowE=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		4w5XQ/pzRLs32mKiutEepOergZU=
		</data>
		<key>Resources/mpv.conf</key>
		<data>
		Fq/G127DQnoYO63v2mzFiG5SSL0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MacOS/lib/libarchive.13.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			aXSTMkcR4knJP4gtdMtJUq+7VwM=
			</data>
			<key>requirement</key>
			<string>cdhash H"697493324711e249c93f882d74cb4952afbb5703"</string>
		</dict>
		<key>MacOS/lib/libaribb24.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			soVglsAPKWUy29xHgsMFEBn0FPI=
			</data>
			<key>requirement</key>
			<string>cdhash H"b2856096c00f296532dbdc4782c3051019f414f2"</string>
		</dict>
		<key>MacOS/lib/libass.9.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			wvFExShXbkoYA7eoSj0lhILUZcE=
			</data>
			<key>requirement</key>
			<string>cdhash H"c2f144c528576e4a1803b7a84a3d258482d465c1"</string>
		</dict>
		<key>MacOS/lib/libavcodec.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZOky/DecRovJ9Kd0Jfe7aFSZ/Qc=
			</data>
			<key>requirement</key>
			<string>cdhash H"64e932fc379c468bc9f4a77425f7bb685499fd07"</string>
		</dict>
		<key>MacOS/lib/libavdevice.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			5Gyd5sPVVlUueQQ+qtm7DnTYF4k=
			</data>
			<key>requirement</key>
			<string>cdhash H"e46c9de6c3d556552e79043eaad9bb0e74d81789"</string>
		</dict>
		<key>MacOS/lib/libavfilter.9.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			n43Y2/hOVk6OrH3yRlVIRIoD4I8=
			</data>
			<key>requirement</key>
			<string>cdhash H"9f8dd8dbf84e564e8eac7df2465548448a03e08f"</string>
		</dict>
		<key>MacOS/lib/libavformat.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			m3joWyVk8wB5rIDBjlSNiQo1l14=
			</data>
			<key>requirement</key>
			<string>cdhash H"9b78e85b2564f30079ac80c18e548d890a35975e"</string>
		</dict>
		<key>MacOS/lib/libavutil.58.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			P+VpjLm6iJWIRmt90MliWiA/HYs=
			</data>
			<key>requirement</key>
			<string>cdhash H"3fe5698cb9ba889588466b7dd0c9625a203f1d8b"</string>
		</dict>
		<key>MacOS/lib/libb2.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			4QlzsEH96LnOxOXctKE5LojAfNo=
			</data>
			<key>requirement</key>
			<string>cdhash H"e10973b041fde8b9cec4e5dcb4a1392e88c07cda"</string>
		</dict>
		<key>MacOS/lib/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			xF//U7yAjLg7VcScf+0qEAy/TCc=
			</data>
			<key>requirement</key>
			<string>cdhash H"c45fff53bc808cb83b55c49c7fed2a100cbf4c27"</string>
		</dict>
		<key>MacOS/lib/libdav1d.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Z61BFTZ8kUwAnt2DqZcyepNoJEA=
			</data>
			<key>requirement</key>
			<string>cdhash H"67ad4115367c914c009edd83a997327a93682440"</string>
		</dict>
		<key>MacOS/lib/libfontconfig.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			DdY12HPSNV8R+smkG/fVHLKh74c=
			</data>
			<key>requirement</key>
			<string>cdhash H"0dd635d873d2355f11fac9a41bf7d51cb2a1ef87"</string>
		</dict>
		<key>MacOS/lib/libfreetype.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			dVjrnq19DKszemzoHbvmUGJBNxQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"7558eb9ead7d0cab337a6ce81dbbe65062413714"</string>
		</dict>
		<key>MacOS/lib/libfribidi.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lTzrCyPSIra7itXhwv3AXhGofHY=
			</data>
			<key>requirement</key>
			<string>cdhash H"953ceb0b23d222b6bb8ad5e1c2fdc05e11a87c76"</string>
		</dict>
		<key>MacOS/lib/libgraphite2.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bMJQZqi6yrjfUtYRlvwICscAroA=
			</data>
			<key>requirement</key>
			<string>cdhash H"6cc25066a8bacab8df52d61196fc080ac700ae80"</string>
		</dict>
		<key>MacOS/lib/libharfbuzz.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			MOlILwz2N3tsx0lc4GE58Zy64BM=
			</data>
			<key>requirement</key>
			<string>cdhash H"30e9482f0cf6377b6cc7495ce06139f19cbae013"</string>
		</dict>
		<key>MacOS/lib/libintl.8.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			gqFs8HL+ToovTlb9ADcMsMyBvBw=
			</data>
			<key>requirement</key>
			<string>cdhash H"82a16cf072fe4e8a2f4e56fd00370cb0cc81bc1c"</string>
		</dict>
		<key>MacOS/lib/libjpeg.8.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			hhvL7BbfTC3NmCSqiYpGw8XsLIU=
			</data>
			<key>requirement</key>
			<string>cdhash H"861bcbec16df4c2dcd9824aa898a46c3c5ec2c85"</string>
		</dict>
		<key>MacOS/lib/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bzzjP1dkM0B72ABHygOxTS3e6qA=
			</data>
			<key>requirement</key>
			<string>cdhash H"6f3ce33f576433407bd80047ca03b14d2ddeeaa0"</string>
		</dict>
		<key>MacOS/lib/liblua.5.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			50Nyp5tWe3zc0HtWSEjY1crV24w=
			</data>
			<key>requirement</key>
			<string>cdhash H"e74372a79b567b7cdcd07b564848d8d5cad5db8c"</string>
		</dict>
		<key>MacOS/lib/liblz4.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			+QFS64J7vWLLwp+HC6A9HhzSy7M=
			</data>
			<key>requirement</key>
			<string>cdhash H"f90152eb827bbd62cbc29f870ba03d1e1cd2cbb3"</string>
		</dict>
		<key>MacOS/lib/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			VfUZupaaJtDis6jVhYjToazeD4M=
			</data>
			<key>requirement</key>
			<string>cdhash H"55f519ba969a26d0e2b3a8d58588d3a1acde0f83"</string>
		</dict>
		<key>MacOS/lib/libmujs.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Vi6s6gQ0a/pLlHtcLKf3KNN5+RU=
			</data>
			<key>requirement</key>
			<string>cdhash H"562eacea04346bfa4b947b5c2ca7f728d379f915"</string>
		</dict>
		<key>MacOS/lib/libogg.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			V8TG/xOkc0LT7AKUik8KAjqg7Jc=
			</data>
			<key>requirement</key>
			<string>cdhash H"57c4c6ff13a47342d3ec02948a4f0a023aa0ec97"</string>
		</dict>
		<key>MacOS/lib/libopenjp2.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			i09qpXf0V22TKofxbduC2crK5sc=
			</data>
			<key>requirement</key>
			<string>cdhash H"8b4f6aa577f4576d932a87f16ddb82d9cacae6c7"</string>
		</dict>
		<key>MacOS/lib/libopus.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			PDnUPLWFGaQNuc6c8XBMbns8SWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"3c39d43cb58519a40db9ce9cf1704c6e7b3c496c"</string>
		</dict>
		<key>MacOS/lib/libplacebo.349.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			tzfAp1qzNVZPaCT34uGlCXQo0Uo=
			</data>
			<key>requirement</key>
			<string>cdhash H"b737c0a75ab335564f6824f7e2e1a5097428d14a"</string>
		</dict>
		<key>MacOS/lib/libpng16.16.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			EQrgIsnROkIjfKZLgUwRaF76ve8=
			</data>
			<key>requirement</key>
			<string>cdhash H"110ae022c9d13a42237ca64b814c11685efabdef"</string>
		</dict>
		<key>MacOS/lib/libpostproc.57.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jWg6t/UdpekoE2Xk9JlYUWUJEvU=
			</data>
			<key>requirement</key>
			<string>cdhash H"8d683ab7f51da5e9281365e4f4995851650912f5"</string>
		</dict>
		<key>MacOS/lib/librubberband.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			0yduZ2iAQDB4lioC1DZ1YewtDJY=
			</data>
			<key>requirement</key>
			<string>cdhash H"d3276e676880403078962a02d4367561ec2d0c96"</string>
		</dict>
		<key>MacOS/lib/libsamplerate.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			eFbPlJfRxKDgeGMZ/gskLzxleCw=
			</data>
			<key>requirement</key>
			<string>cdhash H"7856cf9497d1c4a0e0786319fe0b242f3c65782c"</string>
		</dict>
		<key>MacOS/lib/libshaderc_shared.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			s4etZc7/V+CxwDqu2V1ZNt3XAf8=
			</data>
			<key>requirement</key>
			<string>cdhash H"b387ad65ceff57e0b1c03aaed95d5936ddd701ff"</string>
		</dict>
		<key>MacOS/lib/libsoxr.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			5Rj5Y566ETOH5nulY7CIXsGth9U=
			</data>
			<key>requirement</key>
			<string>cdhash H"e518f9639eba113387e67ba563b0885ec1ad87d5"</string>
		</dict>
		<key>MacOS/lib/libsrt.1.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ywlq3TQP1iIfL/PCiB9smi0hUHQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"cb096add340fd6221f2ff3c2881f6c9a2d215074"</string>
		</dict>
		<key>MacOS/lib/libssh.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			QMspSu5tLiPNPboibEviF/+hV6c=
			</data>
			<key>requirement</key>
			<string>cdhash H"40cb294aee6d2e23cd3dba226c4be217ffa157a7"</string>
		</dict>
		<key>MacOS/lib/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			xgvUyDW/bLczO+7Jnxz+ajn8vXo=
			</data>
			<key>requirement</key>
			<string>cdhash H"c60bd4c835bf6cb7333beec99f1cfe6a39fcbd7a"</string>
		</dict>
		<key>MacOS/lib/libswresample.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bA/b95cZz5Ym34lcBUwcgkcDij4=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c0fdbf79719cf9626df895c054c1c8247038a3e"</string>
		</dict>
		<key>MacOS/lib/libswscale.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			MtiROe82B7nB6ozlvcCk4nzOEWU=
			</data>
			<key>requirement</key>
			<string>cdhash H"32d89139ef3607b9c1ea8ce5bdc0a4e27cce1165"</string>
		</dict>
		<key>MacOS/lib/libuchardet.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			dDCzFBAvlEhkxLci49k9ZEavW1k=
			</data>
			<key>requirement</key>
			<string>cdhash H"7430b314102f944864c4b722e3d93d6446af5b59"</string>
		</dict>
		<key>MacOS/lib/libunibreak.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ti6SINfEbI0OtoMKgD/gK0Q3efQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"b62e9220d7c46c8d0eb6830a803fe02b443779f4"</string>
		</dict>
		<key>MacOS/lib/libvorbis.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GpInyUW6rhf0fgi0aGzVrN4+bCc=
			</data>
			<key>requirement</key>
			<string>cdhash H"1a9227c945baae17f47e08b4686cd5acde3e6c27"</string>
		</dict>
		<key>MacOS/lib/libvpx.8.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			f7XGqQ//H9U21lH7uz50HvDyi6Y=
			</data>
			<key>requirement</key>
			<string>cdhash H"7fb5c6a90fff1fd536d651fbbb3e741ef0f28ba6"</string>
		</dict>
		<key>MacOS/lib/libvulkan.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			SA5CREdT6bhcxu69Xv9V6p6dHKc=
			</data>
			<key>requirement</key>
			<string>cdhash H"480e42444753e9b85cc6eebd5eff55ea9e9d1ca7"</string>
		</dict>
		<key>MacOS/lib/libzimg.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			QkD3Dl4w0JlbGJzXXSQ8m9u8/TI=
			</data>
			<key>requirement</key>
			<string>cdhash H"4240f70e5e30d0995b189cd75d243c9bdbbcfd32"</string>
		</dict>
		<key>MacOS/lib/libzstd.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			zHN9ITLZ+SLIuTPDtVSteGOstUw=
			</data>
			<key>requirement</key>
			<string>cdhash H"cc737d2132d9f922c8b933c3b554ad7863acb54c"</string>
		</dict>
		<key>Resources/document.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			ZN2/HvLHX26NnCwl016uMifqtb0GXRRgln2qzQvwPIg=
			</data>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			sfS102mzGCrik4N/BQ4kX6wn4jrmV+uFyvaVhVenqzM=
			</data>
		</dict>
		<key>Resources/mpv.conf</key>
		<dict>
			<key>hash2</key>
			<data>
			RARcs5inGku5bwKnweLAaTVq2Yxg2ucuOTR+6n6xATU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
