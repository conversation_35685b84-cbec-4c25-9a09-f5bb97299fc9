<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/document.icns</key>
		<data>
		d0HCFnNAzAcI7tSAc8APtdoeowE=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		4w5XQ/pzRLs32mKiutEepOergZU=
		</data>
		<key>Resources/mpv.conf</key>
		<data>
		Fq/G127DQnoYO63v2mzFiG5SSL0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>MacOS/lib/libarchive.13.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			gFIFnY0ruiWlundIuP0AelZTISE=
			</data>
			<key>requirement</key>
			<string>cdhash H"8052059d8d2bba25a5ba7748b8fd007a56532121"</string>
		</dict>
		<key>MacOS/lib/libaribb24.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			17w9zpGI0fPzGVxCCp1fTtJmdNE=
			</data>
			<key>requirement</key>
			<string>cdhash H"d7bc3dce9188d1f3f3195c420a9d5f4ed26674d1"</string>
		</dict>
		<key>MacOS/lib/libass.9.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			zAZIETp2x5Gj4oSaYBUx4520/Tc=
			</data>
			<key>requirement</key>
			<string>cdhash H"cc0648113a76c791a3e2849a601531e39db4fd37"</string>
		</dict>
		<key>MacOS/lib/libavcodec.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			o1yViULWdPEraEoNEs3BDsUDSzI=
			</data>
			<key>requirement</key>
			<string>cdhash H"a35c958942d674f12b684a0d12cdc10ec5034b32"</string>
		</dict>
		<key>MacOS/lib/libavdevice.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			OrH9tPJT/o6SzOiXDd20/HdSphU=
			</data>
			<key>requirement</key>
			<string>cdhash H"3ab1fdb4f253fe8e92cce8970dddb4fc7752a615"</string>
		</dict>
		<key>MacOS/lib/libavfilter.9.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			DvtuVASU+kDVF4WXrzpzT0dyAIA=
			</data>
			<key>requirement</key>
			<string>cdhash H"0efb6e540494fa40d5178597af3a734f47720080"</string>
		</dict>
		<key>MacOS/lib/libavformat.60.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			spLIr8PzHZoN7RW2c9+2OQRz33s=
			</data>
			<key>requirement</key>
			<string>cdhash H"b292c8afc3f31d9a0ded15b673dfb6390473df7b"</string>
		</dict>
		<key>MacOS/lib/libavutil.58.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			TIe+SrL5K3sBzq9jkgKdY27XDh4=
			</data>
			<key>requirement</key>
			<string>cdhash H"4c87be4ab2f92b7b01ceaf6392029d636ed70e1e"</string>
		</dict>
		<key>MacOS/lib/libb2.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			NIiSHa5qufXWb3Ha4BtXl4ZnU/w=
			</data>
			<key>requirement</key>
			<string>cdhash H"3488921dae6ab9f5d66f71dae01b5797866753fc"</string>
		</dict>
		<key>MacOS/lib/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			i/EVgKWIUnMUJXDwpK8iN7ItGso=
			</data>
			<key>requirement</key>
			<string>cdhash H"8bf11580a5885273142570f0a4af2237b22d1aca"</string>
		</dict>
		<key>MacOS/lib/libdav1d.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			mps5gdyLWdsO6jV7jZqnX8uiceE=
			</data>
			<key>requirement</key>
			<string>cdhash H"9a9b3981dc8b59db0eea357b8d9aa75fcba271e1"</string>
		</dict>
		<key>MacOS/lib/libfontconfig.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			5ll3sj2Wq3xsNPw2+dpXlOYiI2s=
			</data>
			<key>requirement</key>
			<string>cdhash H"e65977b23d96ab7c6c34fc36f9da5794e622236b"</string>
		</dict>
		<key>MacOS/lib/libfreetype.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ymssJ7mjsTur5mh2hzFtjF6XkDM=
			</data>
			<key>requirement</key>
			<string>cdhash H"ca6b2c27b9a3b13babe6687687316d8c5e979033"</string>
		</dict>
		<key>MacOS/lib/libfribidi.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			OlVfG1iQv36D5sjEvwTDQnlkFD4=
			</data>
			<key>requirement</key>
			<string>cdhash H"3a555f1b5890bf7e83e6c8c4bf04c3427964143e"</string>
		</dict>
		<key>MacOS/lib/libgraphite2.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Wq9ggUKU1ckPN6ous/p4mS/XEdY=
			</data>
			<key>requirement</key>
			<string>cdhash H"5aaf60814294d5c90f37aa2eb3fa78992fd711d6"</string>
		</dict>
		<key>MacOS/lib/libharfbuzz.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			BKkT4PmErkYL92OD3KqBXV2mrNY=
			</data>
			<key>requirement</key>
			<string>cdhash H"04a913e0f984ae460bf76383dcaa815d5da6acd6"</string>
		</dict>
		<key>MacOS/lib/libintl.8.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ujbXBAWcbf2YwMsRQOhSiv2GYqY=
			</data>
			<key>requirement</key>
			<string>cdhash H"ba36d704059c6dfd98c0cb1140e8528afd8662a6"</string>
		</dict>
		<key>MacOS/lib/libjpeg.8.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			wHWt/Aj+Nl7t3W8KgjG4md+hWcw=
			</data>
			<key>requirement</key>
			<string>cdhash H"c075adfc08fe365eeddd6f0a8231b899dfa159cc"</string>
		</dict>
		<key>MacOS/lib/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			diLNCnjrzexuaiLtXWpo5RDqf9c=
			</data>
			<key>requirement</key>
			<string>cdhash H"7622cd0a78ebcdec6e6a22ed5d6a68e510ea7fd7"</string>
		</dict>
		<key>MacOS/lib/liblua.5.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GwiWT7FKxyNzeozwXvE7uAvjKOo=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b08964fb14ac723737a8cf05ef13bb80be328ea"</string>
		</dict>
		<key>MacOS/lib/liblz4.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			YYP7HQtZcB9OI9knbNpDgTRvud4=
			</data>
			<key>requirement</key>
			<string>cdhash H"6183fb1d0b59701f4e23d9276cda4381346fb9de"</string>
		</dict>
		<key>MacOS/lib/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			UqAD9DdBMGyhLe6c9kMblZXvtXU=
			</data>
			<key>requirement</key>
			<string>cdhash H"52a003f43741306ca12dee9cf6431b9595efb575"</string>
		</dict>
		<key>MacOS/lib/libmujs.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			yyieIG3gTbYeiM+vACyKhizTxic=
			</data>
			<key>requirement</key>
			<string>cdhash H"cb289e206de04db61e88cfaf002c8a862cd3c627"</string>
		</dict>
		<key>MacOS/lib/libogg.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Cpt0uT5/UQujdo5n4o3uqQQSECg=
			</data>
			<key>requirement</key>
			<string>cdhash H"0a9b74b93e7f510ba3768e67e28deea904121028"</string>
		</dict>
		<key>MacOS/lib/libopenjp2.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			YNAf1mnY7pZJaz/HMRQgSBI3tiM=
			</data>
			<key>requirement</key>
			<string>cdhash H"60d01fd669d8ee96496b3fc7311420481237b623"</string>
		</dict>
		<key>MacOS/lib/libopus.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			R/v21PjZ/p1nK4AxcMszuXM3vJU=
			</data>
			<key>requirement</key>
			<string>cdhash H"47fbf6d4f8d9fe9d672b803170cb33b97337bc95"</string>
		</dict>
		<key>MacOS/lib/libplacebo.349.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			1ERr8i5TfpxdwO6kNN0SetNByic=
			</data>
			<key>requirement</key>
			<string>cdhash H"d4446bf22e537e9c5dc0eea434dd127ad341ca27"</string>
		</dict>
		<key>MacOS/lib/libpng16.16.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZzwbECBZWnBPOzKb+Yul9xl5JgE=
			</data>
			<key>requirement</key>
			<string>cdhash H"673c1b1020595a704f3b329bf98ba5f719792601"</string>
		</dict>
		<key>MacOS/lib/libpostproc.57.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8BB5x6p+1B1rqBW9nAuPDKKTRMo=
			</data>
			<key>requirement</key>
			<string>cdhash H"f01079c7aa7ed41d6ba815bd9c0b8f0ca29344ca"</string>
		</dict>
		<key>MacOS/lib/librubberband.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			J8TspFZpEmikUVnlAltiGFg525I=
			</data>
			<key>requirement</key>
			<string>cdhash H"27c4eca456691268a45159e5025b62185839db92"</string>
		</dict>
		<key>MacOS/lib/libsamplerate.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			fChRoVNVOTFWtUAfxL2oyoBvfHs=
			</data>
			<key>requirement</key>
			<string>cdhash H"7c2851a15355393156b5401fc4bda8ca806f7c7b"</string>
		</dict>
		<key>MacOS/lib/libshaderc_shared.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Sj3WmRfwNmdZe/uhlMXFudupo+Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"4a3dd69917f03667597bfba194c5c5b9dba9a3e4"</string>
		</dict>
		<key>MacOS/lib/libsoxr.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			bE+tVzu6kG1Qvo1yOMTr+vZzBDc=
			</data>
			<key>requirement</key>
			<string>cdhash H"6c4fad573bba906d50be8d7238c4ebfaf6730437"</string>
		</dict>
		<key>MacOS/lib/libsrt.1.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Xno6vJXgpkh8K+an4eh5iloq6DM=
			</data>
			<key>requirement</key>
			<string>cdhash H"5e7a3abc95e0a6487c2be6a7e1e8798a5a2ae833"</string>
		</dict>
		<key>MacOS/lib/libssh.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			36y7X0vwmbp91PlcYZyFzfE4+GQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"dfacbb5f4bf099ba7dd4f95c619c85cdf138f864"</string>
		</dict>
		<key>MacOS/lib/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jFVK3TYQ3btQ25HcyhOeiFpahq0=
			</data>
			<key>requirement</key>
			<string>cdhash H"8c554add3610ddbb50db91dcca139e885a5a86ad"</string>
		</dict>
		<key>MacOS/lib/libswresample.4.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			RYwYBtsRJgIlckyZljzR6vz1Bkw=
			</data>
			<key>requirement</key>
			<string>cdhash H"458c1806db11260225724c99963cd1eafcf5064c"</string>
		</dict>
		<key>MacOS/lib/libswscale.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			HVM7YkwhQDpngFrVg4kgJtKrLv4=
			</data>
			<key>requirement</key>
			<string>cdhash H"1d533b624c21403a67805ad583892026d2ab2efe"</string>
		</dict>
		<key>MacOS/lib/libuchardet.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			OQKjqA70Cc1n9SbqUSFVlywAdxQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"3902a3a80ef409cd67f526ea512155972c007714"</string>
		</dict>
		<key>MacOS/lib/libunibreak.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			mziHHRrJpu9mQRPQ853uJnO52jU=
			</data>
			<key>requirement</key>
			<string>cdhash H"9b38871d1ac9a6ef664113d0f39dee2673b9da35"</string>
		</dict>
		<key>MacOS/lib/libvorbis.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			rlxCtfgwx+v9IzsX4wmfZH89E2E=
			</data>
			<key>requirement</key>
			<string>cdhash H"ae5c42b5f830c7ebfd233b17e3099f647f3d1361"</string>
		</dict>
		<key>MacOS/lib/libvpx.11.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			drPh1sKpTnjeQKIa16Qv/0fmdd8=
			</data>
			<key>requirement</key>
			<string>cdhash H"76b3e1d6c2a94e78de40a21ad7a42fff47e675df"</string>
		</dict>
		<key>MacOS/lib/libvulkan.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ojdnx0xYLboduw6gNYeOWdunZaQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"a23767c74c582dba1dbb0ea035878e59dba765a4"</string>
		</dict>
		<key>MacOS/lib/libzimg.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			lHasFh13Rs8IN58c9SAl2djK968=
			</data>
			<key>requirement</key>
			<string>cdhash H"9476ac161d7746cf08379f1cf52025d9d8caf7af"</string>
		</dict>
		<key>MacOS/lib/libzstd.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			H2tUZNYZSGE+ZpnXVtD9m8WO1Wo=
			</data>
			<key>requirement</key>
			<string>cdhash H"1f6b5464d61948613e6699d756d0fd9bc58ed56a"</string>
		</dict>
		<key>Resources/document.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			ZN2/HvLHX26NnCwl016uMifqtb0GXRRgln2qzQvwPIg=
			</data>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			sfS102mzGCrik4N/BQ4kX6wn4jrmV+uFyvaVhVenqzM=
			</data>
		</dict>
		<key>Resources/mpv.conf</key>
		<dict>
			<key>hash2</key>
			<data>
			RARcs5inGku5bwKnweLAaTVq2Yxg2ucuOTR+6n6xATU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
