<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>English</string>
    <key>CFBundleDocumentTypes</key>
    <array>
      <dict>
        <key>CFBundleTypeIconFile</key>
        <string>document.icns</string>
        <key>CFBundleTypeName</key>
        <string>Audio File</string>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>LSTypeIsPackage</key>
        <false/>
        <key>NSPersistentStoreTypeKey</key>
        <string>Binary</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.apple.coreaudio-format</string>
          <string>com.microsoft.waveform-audio</string>
          <string>com.microsoft.windows-media-wma</string>
          <string>io.mpv.dts</string>
          <string>io.mpv.pcm</string>
          <string>org.matroska.mka</string>
          <string>org.xiph.flac</string>
          <string>org.xiph.ogg-audio</string>
          <string>public.aac-audio</string>
          <string>public.ac3-audio</string>
          <string>public.aifc.audio</string>
          <string>public.aiff-audio</string>
          <string>public.audio</string>
          <string>public.mp2</string>
          <string>public.mp3</string>
          <string>public.mpeg-4-audio</string>
          <string>public.ulaw-audio</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleTypeIconFile</key>
        <string>document.icns</string>
        <key>CFBundleTypeName</key>
        <string>Movie File</string>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>LSTypeIsPackage</key>
        <false/>
        <key>NSPersistentStoreTypeKey</key>
        <string>Binary</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.adobe.flash.video</string>
          <string>com.apple.m4v-video</string>
          <string>com.apple.quicktime-movie</string>
          <string>com.microsoft.advanced-systems-format</string>
          <string>com.mythtv.nuv</string>
          <string>com.real.realmedia</string>
          <string>io.mpv.divx</string>
          <string>io.mpv.h263</string>
          <string>io.mpv.h264</string>
          <string>io.mpv.hevc</string>
          <string>io.mpv.mk3d</string>
          <string>io.mpv.mts</string>
          <string>io.mpv.nsv</string>
          <string>io.mpv.vcd</string>
          <string>io.mpv.vob</string>
          <string>io.mpv.webm</string>
          <string>io.mpv.wmv</string>
          <string>io.mpv.xvid</string>
          <string>io.mpv.y4m</string>
          <string>io.mpv.yuv</string>
          <string>org.matroska.mkv</string>
          <string>org.xiph.ogg-video</string>
          <string>public.3gpp2</string>
          <string>public.3gpp</string>
          <string>public.avi</string>
          <string>public.dv-movie</string>
          <string>public.flc-animation</string>
          <string>public.movie</string>
          <string>public.mpeg-2-video</string>
          <string>public.mpeg-4</string>
          <string>public.mpeg</string>
          <string>public.video</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleTypeIconFile</key>
        <string>document.icns</string>
        <key>CFBundleTypeName</key>
        <string>Subtitles File</string>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>LSTypeIsPackage</key>
        <false/>
        <key>NSPersistentStoreTypeKey</key>
        <string>XML</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>io.mpv.aqt</string>
          <string>io.mpv.ass</string>
          <string>io.mpv.jss</string>
          <string>io.mpv.rt</string>
          <string>io.mpv.smi</string>
          <string>io.mpv.subrip</string>
          <string>io.mpv.sub</string>
          <string>io.mpv.vobsub</string>
          <string>public.plain-text</string>
        </array>
      </dict>
    </array>
    <key>CFBundleExecutable</key>
    <string>mpv</string>
    <key>CFBundleIconFile</key>
    <string>icon</string>
    <key>CFBundleIdentifier</key>
    <string>io.mpv</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>mpv</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>0.40.0</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.games</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
      <key>MPVBUNDLE</key>
      <string>true</string>
    </dict>
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>CFBundleURLName</key>
        <string>mpv Custom Protocol</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>mpv</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>CFBundleURLName</key>
        <string>Streaming Protocol</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>mms</string>
          <string>mmst</string>
          <string>http</string>
          <string>https</string>
          <string>httpproxy</string>
          <string>rtp</string>
          <string>rtsp</string>
          <string>ftp</string>
          <string>udp</string>
          <string>smb</string>
          <string>srt</string>
          <string>rist</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleTypeRole</key>
        <string>Viewer</string>
        <key>LSHandlerRank</key>
        <string>Default</string>
        <key>CFBundleURLName</key>
        <string>CD/DVD/Bluray Media</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>cdda</string>
          <string>dvd</string>
          <string>vcd</string>
          <string>bd</string>
        </array>
      </dict>
    </array>
    <key>UTImportedTypeDeclarations</key>
    <array>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>AC3 Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>public.ac3-audio</string>
        <key>UTTypeReferenceURL</key>
        <string>https://wiki.multimedia.cx/index.php?title=AC3</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>ac3</string>
            <string>a52</string>
            <string>eac3</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/ac3</string>
            <string>audio/eac3</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>DTS Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.dts</string>
        <key>UTTypeReferenceURL</key>
        <string>https://wiki.multimedia.cx/index.php?title=DTS</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>dts</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/vnd.dts</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Free Lossless Audio Codec</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>org.xiph.flac</string>
        <key>UTTypeReferenceURL</key>
        <string>https://flac.sourceforge.net/format.html</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>flac</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/flac</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Matroska Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>org.matroska.mka</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.matroska.org</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>mka</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/matroska</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Ogg Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>org.xiph.ogg-audio</string>
        <key>UTTypeReferenceURL</key>
        <string>https://xiph.org/ogg</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>oga</string>
            <string>ogg</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/ogg</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>PCM Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.pcm</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Pulse-code_modulation</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>pcm</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/pcm</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Waveform Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.microsoft.waveform-audio</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/WAV</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>wav</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/wav</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.audio</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Windows Media Audio</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.microsoft.windows-media-wma</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Windows_Media_Audio</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>wma</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>audio/x-ms-wma</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Audio Video Interleave</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>public.avi</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.the-labs.com/Video/odmlff2-avidef.pdf</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>avi</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/x-msvideo</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>DIVX Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.divx</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.divx.com</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>divx</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/divx</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>DV Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>public.dv-movie</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/DV</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>dv</string>
            <string>hdv</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/dv</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Flash Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.adobe.flash.video</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Flash_Video</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>flv</string>
            <string>fla</string>
            <string>f4a</string>
            <string>f4v</string>
            <string>f4b</string>
            <string>f4p</string>
            <string>swf</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>application/vnd.adobe.flash.movie</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>MPEG-2 Transport Stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.mts</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/.m2ts</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>trp</string>
            <string>m2t</string>
            <string>m2ts</string>
            <string>mts</string>
            <string>mtv</string>
            <string>ts</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>model/vnd.mts</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>MPEG-4 File</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.apple.m4v-video</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/M4V</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>m4v</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/m4v</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Matroska Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>org.matroska.mkv</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.matroska.org</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>mkv</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/matroska</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Matroska stereoscopic/3D video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.mk3d</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.matroska.org</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>mk3d</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>application/x-matroska</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
          <string>org.matroska.mkv</string>
        </array>
        <key>UTTypeDescription</key>
        <string>WebM Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.webm</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.webmproject.org</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>webm</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/webm</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Ogg Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>org.xiph.ogg-video</string>
        <key>UTTypeReferenceURL</key>
        <string>https://xiph.org/ogg</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>ogm</string>
            <string>ogv</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/ogg</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Real Media</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.real.realmedia</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.real.com</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>rm</string>
            <string>rmd</string>
            <string>rmj</string>
            <string>rms</string>
            <string>rmvb</string>
            <string>rmx</string>
            <string>rp</string>
            <string>rpm</string>
            <string>rt</string>
            <string>rv</string>
            <string>rvx</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>application/vnd.rn-realmedia-vbr</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Nullsoft Streaming Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.nsv</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Nullsoft</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>nsv</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/x-nsv</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>NuppleVideo File</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>com.mythtv.nuv</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Nullsoft</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>nuv</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Video CD File</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.vcd</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Video_CD</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>vcd</string>
            <string>svcd</string>
            <string>dat</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Video Object</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.vob</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/VOB</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>vob</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/x-ms-vob</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>Windows Media Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.wmv</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/Windows_Media_Video</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>wmv</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/x-ms-wmv</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>XVID Video</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.xvid</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.xvid.org</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>xvid</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/x-xvid</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>H.263 raw stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.h263</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.itu.int/rec/T-REC-H.263</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>263</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/h263</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>AVC raw stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.h264</string>
        <key>UTTypeReferenceURL</key>
        <string>https://www.itu.int/rec/T-REC-H.264</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>264</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>video/H264</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>HEVC raw stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.hevc</string>
        <key>UTTypeReferenceURL</key>
        <string>http://hevc.info</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>hevc</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>YUV stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.yuv</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/YUV</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>yuv</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.movie</string>
        </array>
        <key>UTTypeDescription</key>
        <string>YUV4MPEG2 stream</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.y4m</string>
        <key>UTTypeReferenceURL</key>
        <string>https://wiki.multimedia.cx/index.php?title=YUV4MPEG2</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>y4m</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>SubRip Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.subrip</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/SubRip</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>srt</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>application/x-subrip</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>MicroDVD Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.sub</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/MicroDVD</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>sub</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>text/plain</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>AQTitle Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.aqt</string>
        <key>UTTypeReferenceURL</key>
        <string>https://web.archive.org/web/20070210095721/http://www.volny.cz/aberka/czech/aqt.html</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>aqt</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>text/plain</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>JACOSub Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.jss</string>
        <key>UTTypeReferenceURL</key>
        <string>http://unicorn.us.com/jacosub/jscripts.html</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>jss</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>text/plain</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>RealText Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.rt</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>rt</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>text/plain</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>SubStation Alpha Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.ass</string>
        <key>UTTypeReferenceURL</key>
        <string>https://github.com/libass/libass</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>ass</string>
            <string>ssa</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>text/plain</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.data</string>
        </array>
        <key>UTTypeDescription</key>
        <string>VobSub Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.vobsub</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/DirectVobSub</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>idx</string>
            <string>sub</string>
          </array>
        </dict>
      </dict>
      <dict>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.plain-text</string>
        </array>
        <key>UTTypeDescription</key>
        <string>SAMI Subtitle</string>
        <key>UTTypeIconFile</key>
        <string>document.icns</string>
        <key>UTTypeIdentifier</key>
        <string>io.mpv.smi</string>
        <key>UTTypeReferenceURL</key>
        <string>https://en.wikipedia.org/wiki/SAMI</string>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>smi</string>
            <string>smil</string>
          </array>
          <key>public.mime-type</key>
          <array>
            <string>application/smil</string>
          </array>
        </dict>
      </dict>
    </array>
  </dict>
</plist>
